package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"adc-account-backend/internal/services"
	"adc-account-backend/internal/utils"
)

type DashboardHandler struct {
	dashboardService *services.DashboardService
}

func NewDashboardHandler(dashboardService *services.DashboardService) *DashboardHandler {
	return &DashboardHandler{
		dashboardService: dashboardService,
	}
}

// GetFinancialSummary gets financial summary data
func (h *DashboardHandler) GetFinancialSummary(c *gin.Context) {
	merchantID := c.Query("merchant_id")
	if merchantID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "merchant_id is required", nil)
		return
	}

	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	summary, err := h.dashboardService.GetFinancialSummary(merchantID, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch financial summary", err)
		return
	}

	c.<PERSON>(http.StatusOK, summary)
}

// GetAccountsReceivableSummary gets accounts receivable aging summary
func (h *DashboardHandler) GetAccountsReceivableSummary(c *gin.Context) {
	merchantID := c.Query("merchant_id")
	if merchantID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "merchant_id is required", nil)
		return
	}

	asOfDate := c.Query("as_of_date")

	summary, err := h.dashboardService.GetAccountsReceivableSummary(merchantID, asOfDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch accounts receivable summary", err)
		return
	}

	c.JSON(http.StatusOK, summary)
}

// GetAccountsPayableSummary gets accounts payable aging summary
func (h *DashboardHandler) GetAccountsPayableSummary(c *gin.Context) {
	merchantID := c.Query("merchant_id")
	if merchantID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "merchant_id is required", nil)
		return
	}

	asOfDate := c.Query("as_of_date")

	summary, err := h.dashboardService.GetAccountsPayableSummary(merchantID, asOfDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch accounts payable summary", err)
		return
	}

	c.JSON(http.StatusOK, summary)
}

// GetTopCustomers gets top customers by sales
func (h *DashboardHandler) GetTopCustomers(c *gin.Context) {
	merchantID := c.Query("merchant_id")
	if merchantID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "merchant_id is required", nil)
		return
	}

	limitStr := c.DefaultQuery("limit", "5")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid limit parameter", err)
		return
	}

	period := c.DefaultQuery("period", "30days")

	customers, err := h.dashboardService.GetTopCustomers(merchantID, limit, period)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch top customers", err)
		return
	}

	c.JSON(http.StatusOK, customers)
}

// GetTopVendors gets top vendors by purchases
func (h *DashboardHandler) GetTopVendors(c *gin.Context) {
	merchantID := c.Query("merchant_id")
	if merchantID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "merchant_id is required", nil)
		return
	}

	limitStr := c.DefaultQuery("limit", "5")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid limit parameter", err)
		return
	}

	period := c.DefaultQuery("period", "30days")

	vendors, err := h.dashboardService.GetTopVendors(merchantID, limit, period)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch top vendors", err)
		return
	}

	c.JSON(http.StatusOK, vendors)
}

// GetRecentTransactions gets recent transactions
func (h *DashboardHandler) GetRecentTransactions(c *gin.Context) {
	merchantID := c.Query("merchant_id")
	if merchantID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "merchant_id is required", nil)
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid limit parameter", err)
		return
	}

	transactions, err := h.dashboardService.GetRecentTransactions(merchantID, limit)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch recent transactions", err)
		return
	}

	c.JSON(http.StatusOK, transactions)
}

// GetCashFlow gets cash flow data
func (h *DashboardHandler) GetCashFlow(c *gin.Context) {
	merchantID := c.Query("merchant_id")
	if merchantID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "merchant_id is required", nil)
		return
	}

	period := c.DefaultQuery("period", "6months")

	cashFlow, err := h.dashboardService.GetCashFlow(merchantID, period)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch cash flow data", err)
		return
	}

	c.JSON(http.StatusOK, cashFlow)
}
