package services

import (
	"time"

	"gorm.io/gorm"
)

type DashboardService struct {
	db *gorm.DB
}

func NewDashboardService(db *gorm.DB) *DashboardService {
	return &DashboardService{db: db}
}

// FinancialSummary represents financial overview data
type FinancialSummary struct {
	TotalRevenue       float64 `json:"total_revenue"`
	TotalExpenses      float64 `json:"total_expenses"`
	NetIncome          float64 `json:"net_income"`
	AccountsReceivable float64 `json:"accounts_receivable"`
	AccountsPayable    float64 `json:"accounts_payable"`
	CashBalance        float64 `json:"cash_balance"`
}

// AccountsReceivableSummary represents AR aging data
type AccountsReceivableSummary struct {
	Current    float64 `json:"current"`
	Days1To30  float64 `json:"days_1_30"`
	Days31To60 float64 `json:"days_31_60"`
	Days61To90 float64 `json:"days_61_90"`
	DaysOver90 float64 `json:"days_over_90"`
	Total      float64 `json:"total"`
}

// AccountsPayableSummary represents AP aging data
type AccountsPayableSummary struct {
	Current    float64 `json:"current"`
	Days1To30  float64 `json:"days_1_30"`
	Days31To60 float64 `json:"days_31_60"`
	Days61To90 float64 `json:"days_61_90"`
	DaysOver90 float64 `json:"days_over_90"`
	Total      float64 `json:"total"`
}

// TopCustomer represents top customer data
type TopCustomer struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	TotalSales   float64 `json:"total_sales"`
	OpenInvoices float64 `json:"open_invoices"`
}

// TopVendor represents top vendor data
type TopVendor struct {
	ID             string  `json:"id"`
	Name           string  `json:"name"`
	TotalPurchases float64 `json:"total_purchases"`
	OpenBills      float64 `json:"open_bills"`
}

// RecentTransaction represents recent transaction data
type RecentTransaction struct {
	ID          string    `json:"id"`
	Date        time.Time `json:"date"`
	Description string    `json:"description"`
	Amount      float64   `json:"amount"`
	Type        string    `json:"type"`
}

// CashFlowData represents cash flow chart data
type CashFlowData struct {
	Labels   []string  `json:"labels"`
	Income   []float64 `json:"income"`
	Expenses []float64 `json:"expenses"`
}

// GetFinancialSummary returns financial overview for a merchant
func (s *DashboardService) GetFinancialSummary(merchantID, startDate, endDate string) (*FinancialSummary, error) {
	// For now, return mock data since we don't have all the required models
	// TODO: Implement actual database queries when models are complete

	summary := &FinancialSummary{
		TotalRevenue:       125000.00,
		TotalExpenses:      85000.00,
		NetIncome:          40000.00,
		AccountsReceivable: 25000.00,
		AccountsPayable:    15000.00,
		CashBalance:        50000.00,
	}

	return summary, nil
}

// GetAccountsReceivableSummary returns AR aging summary
func (s *DashboardService) GetAccountsReceivableSummary(merchantID, asOfDate string) (*AccountsReceivableSummary, error) {
	// Mock data for now
	summary := &AccountsReceivableSummary{
		Current:    15000.00,
		Days1To30:  8000.00,
		Days31To60: 3000.00,
		Days61To90: 2000.00,
		DaysOver90: 1000.00,
		Total:      29000.00,
	}

	return summary, nil
}

// GetAccountsPayableSummary returns AP aging summary
func (s *DashboardService) GetAccountsPayableSummary(merchantID, asOfDate string) (*AccountsPayableSummary, error) {
	// Mock data for now
	summary := &AccountsPayableSummary{
		Current:    12000.00,
		Days1To30:  5000.00,
		Days31To60: 2000.00,
		Days61To90: 1000.00,
		DaysOver90: 500.00,
		Total:      20500.00,
	}

	return summary, nil
}

// GetTopCustomers returns top customers by sales
func (s *DashboardService) GetTopCustomers(merchantID string, limit int, period string) ([]TopCustomer, error) {
	// Mock data for now
	customers := []TopCustomer{
		{ID: "1", Name: "ABC Corporation", TotalSales: 45000.00, OpenInvoices: 5000.00},
		{ID: "2", Name: "XYZ Industries", TotalSales: 32000.00, OpenInvoices: 3200.00},
		{ID: "3", Name: "Tech Solutions Ltd", TotalSales: 28000.00, OpenInvoices: 2800.00},
		{ID: "4", Name: "Global Services Inc", TotalSales: 22000.00, OpenInvoices: 2200.00},
		{ID: "5", Name: "Innovation Partners", TotalSales: 18000.00, OpenInvoices: 1800.00},
	}

	if limit < len(customers) {
		customers = customers[:limit]
	}

	return customers, nil
}

// GetTopVendors returns top vendors by purchases
func (s *DashboardService) GetTopVendors(merchantID string, limit int, period string) ([]TopVendor, error) {
	// Mock data for now
	vendors := []TopVendor{
		{ID: "1", Name: "Office Supplies Co", TotalPurchases: 15000.00, OpenBills: 1500.00},
		{ID: "2", Name: "Tech Equipment Ltd", TotalPurchases: 12000.00, OpenBills: 1200.00},
		{ID: "3", Name: "Utilities Provider", TotalPurchases: 8000.00, OpenBills: 800.00},
		{ID: "4", Name: "Marketing Agency", TotalPurchases: 6000.00, OpenBills: 600.00},
		{ID: "5", Name: "Legal Services", TotalPurchases: 4000.00, OpenBills: 400.00},
	}

	if limit < len(vendors) {
		vendors = vendors[:limit]
	}

	return vendors, nil
}

// GetRecentTransactions returns recent transactions
func (s *DashboardService) GetRecentTransactions(merchantID string, limit int) ([]RecentTransaction, error) {
	// Mock data for now
	now := time.Now()
	transactions := []RecentTransaction{
		{ID: "1", Date: now.AddDate(0, 0, -1), Description: "Payment from ABC Corp", Amount: 5000.00, Type: "income"},
		{ID: "2", Date: now.AddDate(0, 0, -2), Description: "Office rent payment", Amount: -2000.00, Type: "expense"},
		{ID: "3", Date: now.AddDate(0, 0, -3), Description: "Invoice payment received", Amount: 3200.00, Type: "income"},
		{ID: "4", Date: now.AddDate(0, 0, -4), Description: "Equipment purchase", Amount: -1500.00, Type: "expense"},
		{ID: "5", Date: now.AddDate(0, 0, -5), Description: "Service fee payment", Amount: 2800.00, Type: "income"},
	}

	if limit < len(transactions) {
		transactions = transactions[:limit]
	}

	return transactions, nil
}

// GetCashFlow returns cash flow data for charts
func (s *DashboardService) GetCashFlow(merchantID, period string) (*CashFlowData, error) {
	// Mock data for now - 6 months of data
	labels := []string{"Jan", "Feb", "Mar", "Apr", "May", "Jun"}
	income := []float64{25000, 28000, 32000, 30000, 35000, 38000}
	expenses := []float64{18000, 20000, 22000, 21000, 24000, 26000}

	cashFlow := &CashFlowData{
		Labels:   labels,
		Income:   income,
		Expenses: expenses,
	}

	return cashFlow, nil
}
