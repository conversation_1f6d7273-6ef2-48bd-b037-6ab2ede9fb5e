package routes

import (
	"adc-account-backend/internal/handlers"
	"adc-account-backend/internal/middleware"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all API routes
func SetupRoutes(router *gin.RouterGroup, container *services.Container) {
	// Initialize handlers
	authHandler := handlers.NewAuthHandler(container.AuthService)
	userHandler := handlers.NewUserHandler(container.UserService)
	organizationHandler := handlers.NewOrganizationHandler(container.OrganizationService)
	branchHandler := handlers.NewBranchHandler(container.BranchService)
	merchantHandler := handlers.NewMerchantHandler(container.MerchantService)
	invoiceHandler := handlers.NewInvoiceHandler(container.InvoiceService)
	billHandler := handlers.NewBillHandler(container.BillService)
	expenseHandler := handlers.NewExpenseHandler(container.ExpenseService)
	customerHandler := handlers.NewCustomerHandler(container.CustomerService)
	vendorHandler := handlers.NewVendorHandler(container.VendorService)
	accountHandler := handlers.NewAccountHandler(container.AccountService)
	journalEntryHandler := handlers.NewJournalEntryHandler(container.JournalEntryService)
	taxRateHandler := handlers.NewTaxRateHandler(container.TaxRateService)
	bankAccountHandler := handlers.NewBankAccountHandler(container.BankAccountService)
	bankTransactionHandler := handlers.NewBankTransactionHandler(container.BankTransactionService)
	bankReconciliationHandler := handlers.NewBankReconciliationHandler(container.BankReconciliationService)
	assetHandler := handlers.NewAssetHandler(container.AssetService)
	inventoryHandler := handlers.NewInventoryHandler(container.InventoryService)
	payrollHandler := handlers.NewPayrollHandler(container.PayrollService)
	budgetHandler := handlers.NewBudgetHandler(container.BudgetService)
	collectionCaseHandler := handlers.NewCollectionCaseHandler(container.CollectionCaseService)
	collectionActivityHandler := handlers.NewCollectionActivityHandler(container.CollectionActivityService)
	collectionTemplateHandler := handlers.NewCollectionTemplateHandler(container.CollectionTemplateService)
	recurringInvoiceHandler := handlers.NewRecurringInvoiceHandler(container.RecurringInvoiceService)
	budgetTemplateHandler := handlers.NewBudgetTemplateHandler(container.BudgetTemplateService)
	dashboardHandler := handlers.NewDashboardHandler(container.DashboardService)

	// Authentication routes (no auth required)
	auth := router.Group("/auth")
	auth.Use(middleware.AuthRateLimit())
	{
		auth.POST("/login", authHandler.Login)
		auth.POST("/register", authHandler.Register)
		auth.POST("/refresh", authHandler.RefreshToken)
		auth.POST("/logout", middleware.AuthMiddleware(container.Config), authHandler.Logout)
		auth.GET("/me", middleware.AuthMiddleware(container.Config), authHandler.GetCurrentUser)
	}

	// Protected routes
	protected := router.Group("")
	protected.Use(middleware.AuthMiddleware(container.Config))

	// User management routes
	users := protected.Group("/users")
	{
		users.GET("", userHandler.GetAllUsers)
		users.GET("/:id", userHandler.GetUserByID)
		users.POST("", userHandler.CreateUser)
		users.PUT("/:id", userHandler.UpdateUser)
		users.DELETE("/:id", userHandler.DeleteUser)
		users.PUT("/:id/preferences", userHandler.UpdateUserPreferences)
	}

	// Organization routes
	organizations := protected.Group("/organizations")
	{
		organizations.GET("", organizationHandler.GetAllOrganizations)
		organizations.GET("/:id", organizationHandler.GetOrganizationByID)
		organizations.POST("", organizationHandler.CreateOrganization)
		organizations.PUT("/:id", organizationHandler.UpdateOrganization)
		organizations.DELETE("/:id", organizationHandler.DeleteOrganization)

		// User management within organizations
		organizations.POST("/:id/users", organizationHandler.AddUserToOrganization)
		organizations.DELETE("/:id/users/:userId", organizationHandler.RemoveUserFromOrganization)

		// Branches under organization
		organizations.GET("/:id/branches", branchHandler.GetBranchesByOrganization)
		organizations.POST("/:id/branches", branchHandler.CreateBranch)
		organizations.PUT("/:id/branches/:branchId", branchHandler.UpdateBranch)
		organizations.DELETE("/:id/branches/:branchId", branchHandler.DeleteBranch)
	}

	// Branch routes
	branches := protected.Group("/branches")
	{
		branches.GET("", branchHandler.GetAllBranches)
		branches.GET("/:id", branchHandler.GetBranchByID)
	}

	// Merchant routes
	merchants := protected.Group("/merchants")
	{
		merchants.GET("", merchantHandler.GetAllMerchants)
		merchants.GET("/:id", merchantHandler.GetMerchantByID)
		merchants.POST("", merchantHandler.CreateMerchant)
		merchants.PUT("/:id", merchantHandler.UpdateMerchant)
		merchants.DELETE("/:id", merchantHandler.DeleteMerchant)

		// Customers under merchant
		merchants.GET("/:id/customers", customerHandler.GetCustomersByMerchant)
		merchants.POST("/:id/customers", customerHandler.CreateCustomer)
		merchants.PUT("/:id/customers/:customerId", customerHandler.UpdateCustomer)
		merchants.DELETE("/:id/customers/:customerId", customerHandler.DeleteCustomer)

		// Vendors under merchant
		merchants.GET("/:id/vendors", vendorHandler.GetVendorsByMerchant)
		merchants.POST("/:id/vendors", vendorHandler.CreateVendor)
		merchants.PUT("/:id/vendors/:vendorId", vendorHandler.UpdateVendor)
		merchants.DELETE("/:id/vendors/:vendorId", vendorHandler.DeleteVendor)

		// Invoices under merchant
		merchants.GET("/:id/invoices", invoiceHandler.GetInvoicesByMerchant)
		merchants.POST("/:id/invoices", invoiceHandler.CreateInvoice)
		merchants.PUT("/:id/invoices/:invoiceId", invoiceHandler.UpdateInvoice)
		merchants.DELETE("/:id/invoices/:invoiceId", invoiceHandler.DeleteInvoice)
		merchants.POST("/:id/invoices/:invoiceId/payments", invoiceHandler.AddInvoicePayment)

		// Bills under merchant
		merchants.GET("/:id/bills", billHandler.GetBillsByMerchant)
		merchants.POST("/:id/bills", billHandler.CreateBill)
		merchants.PUT("/:id/bills/:billId", billHandler.UpdateBill)
		merchants.DELETE("/:id/bills/:billId", billHandler.DeleteBill)
		merchants.POST("/:id/bills/:billId/payments", billHandler.AddBillPayment)

		// Expenses under merchant
		merchants.GET("/:id/expenses", expenseHandler.GetExpensesByMerchant)
		merchants.POST("/:id/expenses", expenseHandler.CreateExpense)
		merchants.PUT("/:id/expenses/:expenseId", expenseHandler.UpdateExpense)
		merchants.DELETE("/:id/expenses/:expenseId", expenseHandler.DeleteExpense)

		// Chart of Accounts under merchant
		merchants.GET("/:id/accounts", accountHandler.GetAccountsByMerchant)
		merchants.POST("/:id/accounts", accountHandler.CreateAccount)
		merchants.PUT("/:id/accounts/:accountId", accountHandler.UpdateAccount)
		merchants.DELETE("/:id/accounts/:accountId", accountHandler.DeleteAccount)

		// Journal Entries under merchant
		merchants.GET("/:id/journal-entries", journalEntryHandler.GetJournalEntriesByMerchant)
		merchants.POST("/:id/journal-entries", journalEntryHandler.CreateJournalEntry)
		merchants.PUT("/:id/journal-entries/:entryId", journalEntryHandler.UpdateJournalEntry)
		merchants.DELETE("/:id/journal-entries/:entryId", journalEntryHandler.DeleteJournalEntry)

		// Tax Rates under merchant
		merchants.GET("/:id/tax-rates", taxRateHandler.GetTaxRatesByMerchant)
		merchants.POST("/:id/tax-rates", taxRateHandler.CreateTaxRate)
		merchants.PUT("/:id/tax-rates/:taxRateId", taxRateHandler.UpdateTaxRate)
		merchants.DELETE("/:id/tax-rates/:taxRateId", taxRateHandler.DeleteTaxRate)
		merchants.GET("/:id/tax-rates/default", taxRateHandler.GetDefaultTaxRate)

		// Bank Accounts under merchant
		merchants.GET("/:id/bank-accounts", bankAccountHandler.GetBankAccountsByMerchant)
		merchants.POST("/:id/bank-accounts", bankAccountHandler.CreateBankAccount)
		merchants.PUT("/:id/bank-accounts/:bankAccountId", bankAccountHandler.UpdateBankAccount)
		merchants.DELETE("/:id/bank-accounts/:bankAccountId", bankAccountHandler.DeleteBankAccount)

		// Bank Transactions under merchant
		merchants.GET("/:id/bank-transactions", bankTransactionHandler.GetBankTransactionsByMerchant)
		merchants.POST("/:id/bank-transactions", bankTransactionHandler.CreateBankTransaction)
		merchants.PUT("/:id/bank-transactions/:transactionId", bankTransactionHandler.UpdateBankTransaction)
		merchants.DELETE("/:id/bank-transactions/:transactionId", bankTransactionHandler.DeleteBankTransaction)

		// Bank Reconciliations under merchant
		merchants.GET("/:id/bank-reconciliations", bankReconciliationHandler.GetBankReconciliationsByMerchant)
		merchants.POST("/:id/bank-reconciliations", bankReconciliationHandler.CreateBankReconciliation)
		merchants.PUT("/:id/bank-reconciliations/:reconciliationId", bankReconciliationHandler.UpdateBankReconciliation)
		merchants.DELETE("/:id/bank-reconciliations/:reconciliationId", bankReconciliationHandler.DeleteBankReconciliation)

		// Assets under merchant
		merchants.GET("/:id/assets", assetHandler.GetAssetsByMerchant)
		merchants.POST("/:id/assets", assetHandler.CreateAsset)
		merchants.PUT("/:id/assets/:assetId", assetHandler.UpdateAsset)
		merchants.DELETE("/:id/assets/:assetId", assetHandler.DeleteAsset)

		// Inventory Items under merchant
		merchants.GET("/:id/inventory-items", inventoryHandler.GetInventoryItemsByMerchant)
		merchants.POST("/:id/inventory-items", inventoryHandler.CreateInventoryItem)
		merchants.PUT("/:id/inventory-items/:itemId", inventoryHandler.UpdateInventoryItem)
		merchants.DELETE("/:id/inventory-items/:itemId", inventoryHandler.DeleteInventoryItem)

		// Inventory Transactions under merchant
		merchants.GET("/:id/inventory-transactions", inventoryHandler.GetInventoryTransactionsByMerchant)
		merchants.POST("/:id/inventory-transactions", inventoryHandler.CreateInventoryTransaction)
		merchants.PUT("/:id/inventory-transactions/:transactionId", inventoryHandler.UpdateInventoryTransaction)
		merchants.DELETE("/:id/inventory-transactions/:transactionId", inventoryHandler.DeleteInventoryTransaction)

		// Employees under merchant
		merchants.GET("/:id/employees", payrollHandler.GetEmployeesByMerchant)
		merchants.POST("/:id/employees", payrollHandler.CreateEmployee)
		merchants.PUT("/:id/employees/:employeeId", payrollHandler.UpdateEmployee)
		merchants.DELETE("/:id/employees/:employeeId", payrollHandler.DeleteEmployee)

		// Payroll Runs under merchant
		merchants.GET("/:id/payroll-runs", payrollHandler.GetPayrollRunsByMerchant)
		merchants.POST("/:id/payroll-runs", payrollHandler.CreatePayrollRun)
		merchants.PUT("/:id/payroll-runs/:payrollRunId", payrollHandler.UpdatePayrollRun)
		merchants.DELETE("/:id/payroll-runs/:payrollRunId", payrollHandler.DeletePayrollRun)

		// Payroll Details under merchant
		merchants.GET("/:id/payroll-details", payrollHandler.GetPayrollDetailsByMerchant)
		merchants.POST("/:id/payroll-details", payrollHandler.CreatePayrollDetail)
		merchants.PUT("/:id/payroll-details/:payrollDetailId", payrollHandler.UpdatePayrollDetail)
		merchants.DELETE("/:id/payroll-details/:payrollDetailId", payrollHandler.DeletePayrollDetail)

		// Budget Items under merchant
		merchants.GET("/:id/budget-items", budgetHandler.GetBudgetItemsByMerchant)
		merchants.POST("/:id/budget-items", budgetHandler.CreateBudgetItem)
		merchants.PUT("/:id/budget-items/:budgetItemId", budgetHandler.UpdateBudgetItem)
		merchants.DELETE("/:id/budget-items/:budgetItemId", budgetHandler.DeleteBudgetItem)
		merchants.GET("/:id/budget-report/:year/:month", budgetHandler.GetBudgetReport)
		merchants.GET("/:id/budget-summary", budgetHandler.GetBudgetSummary)
		merchants.POST("/:id/budget-actual/:year/:month", budgetHandler.UpdateActualAmounts)

		// Collection Cases under merchant
		merchants.GET("/:id/collection-cases", collectionCaseHandler.GetCollectionCasesByMerchant)
		merchants.POST("/:id/collection-cases", collectionCaseHandler.CreateCollectionCase)
		merchants.PUT("/:id/collection-cases/:caseId", collectionCaseHandler.UpdateCollectionCase)
		merchants.DELETE("/:id/collection-cases/:caseId", collectionCaseHandler.DeleteCollectionCase)

		// Collection Templates under merchant
		merchants.GET("/:id/collection-templates", collectionTemplateHandler.GetCollectionTemplatesByMerchant)
		merchants.POST("/:id/collection-templates", collectionTemplateHandler.CreateCollectionTemplate)
		merchants.PUT("/:id/collection-templates/:templateId", collectionTemplateHandler.UpdateCollectionTemplate)
		merchants.DELETE("/:id/collection-templates/:templateId", collectionTemplateHandler.DeleteCollectionTemplate)
		merchants.GET("/:id/collection-templates/active", collectionTemplateHandler.GetActiveCollectionTemplates)

		// Budget Templates under merchant
		merchants.GET("/:id/budget-templates", budgetTemplateHandler.GetBudgetTemplatesByMerchant)
		merchants.POST("/:id/budget-templates", budgetTemplateHandler.CreateBudgetTemplate)
		merchants.PUT("/:id/budget-templates/:templateId", budgetTemplateHandler.UpdateBudgetTemplate)
		merchants.DELETE("/:id/budget-templates/:templateId", budgetTemplateHandler.DeleteBudgetTemplate)
		merchants.GET("/:id/budget-templates/active", budgetTemplateHandler.GetActiveBudgetTemplates)

		// Recurring Invoices under merchant
		merchants.GET("/:id/recurring-invoices", recurringInvoiceHandler.GetRecurringInvoicesByMerchant)
		merchants.POST("/:id/recurring-invoices", recurringInvoiceHandler.CreateRecurringInvoice)
		merchants.PUT("/:id/recurring-invoices/:recurringInvoiceId", recurringInvoiceHandler.UpdateRecurringInvoice)
		merchants.DELETE("/:id/recurring-invoices/:recurringInvoiceId", recurringInvoiceHandler.DeleteRecurringInvoice)
		merchants.POST("/:id/recurring-invoices/:recurringInvoiceId/generate", recurringInvoiceHandler.GenerateInvoice)
		merchants.PUT("/:id/recurring-invoices/:recurringInvoiceId/status", recurringInvoiceHandler.UpdateRecurringInvoiceStatus)
	}

	// Global routes (not merchant-specific)
	customers := protected.Group("/customers")
	{
		customers.GET("", customerHandler.GetAllCustomers)
		customers.GET("/:id", customerHandler.GetCustomerByID)
	}

	vendors := protected.Group("/vendors")
	{
		vendors.GET("", vendorHandler.GetAllVendors)
		vendors.GET("/:id", vendorHandler.GetVendorByID)
	}

	invoices := protected.Group("/invoices")
	{
		invoices.GET("", invoiceHandler.GetAllInvoices)
		invoices.GET("/:id", invoiceHandler.GetInvoiceByID)
	}

	bills := protected.Group("/bills")
	{
		bills.GET("", billHandler.GetAllBills)
		bills.GET("/:id", billHandler.GetBillByID)
	}

	expenses := protected.Group("/expenses")
	{
		expenses.GET("", expenseHandler.GetAllExpenses)
		expenses.GET("/:id", expenseHandler.GetExpenseByID)
	}

	accounts := protected.Group("/accounts")
	{
		accounts.GET("", accountHandler.GetAllAccounts)
		accounts.GET("/:id", accountHandler.GetAccountByID)
	}

	journalEntries := protected.Group("/journal-entries")
	{
		journalEntries.GET("", journalEntryHandler.GetAllJournalEntries)
		journalEntries.GET("/:id", journalEntryHandler.GetJournalEntryByID)
	}

	taxRates := protected.Group("/tax-rates")
	{
		taxRates.GET("", taxRateHandler.GetAllTaxRates)
		taxRates.GET("/:id", taxRateHandler.GetTaxRateByID)
		taxRates.GET("/calculate/:taxRateId", taxRateHandler.CalculateTax)
	}

	bankAccounts := protected.Group("/bank-accounts")
	{
		bankAccounts.GET("", bankAccountHandler.GetAllBankAccounts)
		bankAccounts.GET("/:id", bankAccountHandler.GetBankAccountByID)
	}

	bankTransactions := protected.Group("/bank-transactions")
	{
		bankTransactions.GET("", bankTransactionHandler.GetAllBankTransactions)
		bankTransactions.GET("/:id", bankTransactionHandler.GetBankTransactionByID)
	}

	bankReconciliations := protected.Group("/bank-reconciliations")
	{
		bankReconciliations.GET("", bankReconciliationHandler.GetAllBankReconciliations)
		bankReconciliations.GET("/:id", bankReconciliationHandler.GetBankReconciliationByID)
	}

	assets := protected.Group("/assets")
	{
		assets.GET("", assetHandler.GetAllAssets)
		assets.GET("/:id", assetHandler.GetAssetByID)
	}

	inventoryItems := protected.Group("/inventory-items")
	{
		inventoryItems.GET("", inventoryHandler.GetAllInventoryItems)
		inventoryItems.GET("/:id", inventoryHandler.GetInventoryItemByID)
		inventoryItems.POST("", inventoryHandler.CreateInventoryItem)
	}

	inventoryTransactions := protected.Group("/inventory-transactions")
	{
		inventoryTransactions.GET("", inventoryHandler.GetAllInventoryTransactions)
		inventoryTransactions.GET("/:id", inventoryHandler.GetInventoryTransactionByID)
		inventoryTransactions.POST("", inventoryHandler.CreateInventoryTransaction)
	}

	employees := protected.Group("/employees")
	{
		employees.GET("", payrollHandler.GetAllEmployees)
		employees.GET("/:id", payrollHandler.GetEmployeeByID)
		employees.POST("", payrollHandler.CreateEmployee)
	}

	payrollRuns := protected.Group("/payroll-runs")
	{
		payrollRuns.GET("", payrollHandler.GetAllPayrollRuns)
		payrollRuns.GET("/:id", payrollHandler.GetPayrollRunByID)
		payrollRuns.POST("", payrollHandler.CreatePayrollRun)
	}

	payrollDetails := protected.Group("/payroll-details")
	{
		payrollDetails.GET("", payrollHandler.GetAllPayrollDetails)
		payrollDetails.GET("/:id", payrollHandler.GetPayrollDetailByID)
	}

	budgetItems := protected.Group("/budget-items")
	{
		budgetItems.GET("", budgetHandler.GetAllBudgetItems)
		budgetItems.GET("/:id", budgetHandler.GetBudgetItemByID)
	}

	collectionCases := protected.Group("/collection-cases")
	{
		collectionCases.GET("", collectionCaseHandler.GetAllCollectionCases)
		collectionCases.GET("/:id", collectionCaseHandler.GetCollectionCaseByID)
	}

	collectionActivities := protected.Group("/collection-activities")
	{
		collectionActivities.GET("", collectionActivityHandler.GetAllCollectionActivities)
		collectionActivities.GET("/:id", collectionActivityHandler.GetCollectionActivityByID)
		collectionActivities.POST("", collectionActivityHandler.CreateCollectionActivity)
		collectionActivities.PUT("/:id", collectionActivityHandler.UpdateCollectionActivity)
		collectionActivities.DELETE("/:id", collectionActivityHandler.DeleteCollectionActivity)
		collectionActivities.GET("/summary", collectionActivityHandler.GetActivitySummary)
		collectionActivities.GET("/case/:caseId", collectionActivityHandler.GetCollectionActivitiesByCase)
		collectionActivities.GET("/creator/:creatorId", collectionActivityHandler.GetCollectionActivitiesByCreator)
	}

	recurringInvoices := protected.Group("/recurring-invoices")
	{
		recurringInvoices.GET("", recurringInvoiceHandler.GetAllRecurringInvoices)
		recurringInvoices.GET("/:id", recurringInvoiceHandler.GetRecurringInvoiceByID)
		recurringInvoices.PUT("/:id", recurringInvoiceHandler.UpdateRecurringInvoice)
		recurringInvoices.DELETE("/:id", recurringInvoiceHandler.DeleteRecurringInvoice)
		recurringInvoices.POST("/:id/generate", recurringInvoiceHandler.GenerateInvoice)
		recurringInvoices.PUT("/:id/status", recurringInvoiceHandler.UpdateRecurringInvoiceStatus)
		recurringInvoices.GET("/customer/:customerId", recurringInvoiceHandler.GetRecurringInvoicesByCustomer)
	}

	collectionTemplates := protected.Group("/collection-templates")
	{
		collectionTemplates.GET("", collectionTemplateHandler.GetAllCollectionTemplates)
		collectionTemplates.GET("/:id", collectionTemplateHandler.GetCollectionTemplateByID)
		collectionTemplates.PUT("/:id", collectionTemplateHandler.UpdateCollectionTemplate)
		collectionTemplates.DELETE("/:id", collectionTemplateHandler.DeleteCollectionTemplate)
		collectionTemplates.GET("/:id/steps", collectionTemplateHandler.GetCollectionTemplateSteps)
		collectionTemplates.POST("/:id/steps", collectionTemplateHandler.CreateCollectionTemplateStep)
		collectionTemplates.PUT("/:id/steps/:stepId", collectionTemplateHandler.UpdateCollectionTemplateStep)
		collectionTemplates.DELETE("/:id/steps/:stepId", collectionTemplateHandler.DeleteCollectionTemplateStep)
	}

	budgetTemplates := protected.Group("/budget-templates")
	{
		budgetTemplates.GET("", budgetTemplateHandler.GetAllBudgetTemplates)
		budgetTemplates.GET("/:id", budgetTemplateHandler.GetBudgetTemplateByID)
		budgetTemplates.PUT("/:id", budgetTemplateHandler.UpdateBudgetTemplate)
		budgetTemplates.DELETE("/:id", budgetTemplateHandler.DeleteBudgetTemplate)
		budgetTemplates.POST("/:id/apply", budgetTemplateHandler.ApplyBudgetTemplate)
		budgetTemplates.GET("/:id/items", budgetTemplateHandler.GetBudgetTemplateItems)
		budgetTemplates.POST("/:id/items", budgetTemplateHandler.CreateBudgetTemplateItem)
		budgetTemplates.PUT("/:id/items/:itemId", budgetTemplateHandler.UpdateBudgetTemplateItem)
		budgetTemplates.DELETE("/:id/items/:itemId", budgetTemplateHandler.DeleteBudgetTemplateItem)
	}

	// Dashboard routes
	dashboard := protected.Group("/dashboard")
	{
		dashboard.GET("/financial-summary", dashboardHandler.GetFinancialSummary)
		dashboard.GET("/accounts-receivable-summary", dashboardHandler.GetAccountsReceivableSummary)
		dashboard.GET("/accounts-payable-summary", dashboardHandler.GetAccountsPayableSummary)
		dashboard.GET("/top-customers", dashboardHandler.GetTopCustomers)
		dashboard.GET("/top-vendors", dashboardHandler.GetTopVendors)
		dashboard.GET("/recent-transactions", dashboardHandler.GetRecentTransactions)
		dashboard.GET("/cash-flow", dashboardHandler.GetCashFlow)
	}
}
