/**
 * Test Setup Verification Script
 * 
 * This script verifies that the test environment is properly configured
 * and the global test account is accessible.
 */

import { GLOBAL_TEST_ACCOUNT, TEST_CONFIG, createTestAccount } from './config/test-accounts';

async function verifyBackend(): Promise<boolean> {
  try {
    console.log('🔍 Checking backend health...');
    const response = await fetch(`${TEST_CONFIG.BACKEND_URL}/health`);
    if (response.ok) {
      console.log('✅ Backend is running');
      return true;
    } else {
      console.log('❌ Backend health check failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Backend is not accessible:', error);
    return false;
  }
}

async function verifyFrontend(): Promise<boolean> {
  try {
    console.log('🔍 Checking frontend...');
    const response = await fetch(TEST_CONFIG.FRONTEND_URL);
    if (response.ok) {
      console.log('✅ Frontend is running');
      return true;
    } else {
      console.log('❌ Frontend is not accessible');
      return false;
    }
  } catch (error) {
    console.log('❌ Frontend is not accessible:', error);
    return false;
  }
}

async function verifyTestAccount(): Promise<boolean> {
  try {
    console.log('🔍 Verifying global test account...');
    
    // Try to login with the global test account
    const response = await fetch(`${TEST_CONFIG.BACKEND_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: GLOBAL_TEST_ACCOUNT.email,
        password: GLOBAL_TEST_ACCOUNT.password,
      }),
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Global test account is accessible');
      console.log(`   User ID: ${result.user.id}`);
      console.log(`   Email: ${result.user.email}`);
      console.log(`   Name: ${result.user.name}`);
      return true;
    } else {
      console.log('❌ Global test account login failed');
      const errorText = await response.text();
      console.log(`   Error: ${errorText}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Error verifying test account:', error);
    return false;
  }
}

async function createAdditionalTestAccounts(): Promise<void> {
  console.log('🔍 Creating additional test accounts...');
  
  const additionalAccounts = [
    {
      name: 'Test Admin User',
      email: '<EMAIL>',
      password: 'adminpassword123',
      role: 'admin',
      description: 'Admin test account'
    },
    {
      name: 'Test Manager User',
      email: '<EMAIL>', 
      password: 'managerpassword123',
      role: 'manager',
      description: 'Manager test account'
    }
  ];

  for (const account of additionalAccounts) {
    try {
      const response = await fetch(`${TEST_CONFIG.BACKEND_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: account.name,
          email: account.email,
          password: account.password,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Created ${account.role} test account: ${account.email}`);
        console.log(`   User ID: ${result.user.id}`);
      } else if (response.status === 400) {
        // Account might already exist
        console.log(`ℹ️  ${account.role} test account already exists: ${account.email}`);
      } else {
        console.log(`❌ Failed to create ${account.role} test account: ${account.email}`);
      }
    } catch (error) {
      console.log(`❌ Error creating ${account.role} test account:`, error);
    }
  }
}

async function verifyTestDirectories(): Promise<void> {
  console.log('🔍 Verifying test directory structure...');
  
  const fs = require('fs');
  const path = require('path');
  
  const requiredDirs = [
    'tests/config',
    'tests/e2e',
    'tests/e2e/auth',
    'tests/screenshots'
  ];
  
  for (const dir of requiredDirs) {
    if (!fs.existsSync(dir)) {
      console.log(`📁 Creating directory: ${dir}`);
      fs.mkdirSync(dir, { recursive: true });
    } else {
      console.log(`✅ Directory exists: ${dir}`);
    }
  }
}

async function main(): Promise<void> {
  console.log('🚀 Starting test setup verification...\n');
  
  // Verify directories
  await verifyTestDirectories();
  console.log('');
  
  // Verify backend
  const backendOk = await verifyBackend();
  console.log('');
  
  // Verify frontend
  const frontendOk = await verifyFrontend();
  console.log('');
  
  if (!backendOk || !frontendOk) {
    console.log('❌ Environment verification failed. Please ensure both backend and frontend are running.');
    console.log('   Backend: cd go-backend && go run main.go');
    console.log('   Frontend: npm run dev');
    process.exit(1);
  }
  
  // Verify test account
  const testAccountOk = await verifyTestAccount();
  console.log('');
  
  if (!testAccountOk) {
    console.log('❌ Global test account verification failed.');
    console.log('   The account might not exist or credentials might be incorrect.');
    process.exit(1);
  }
  
  // Create additional test accounts
  await createAdditionalTestAccounts();
  console.log('');
  
  // Final summary
  console.log('🎉 Test setup verification completed successfully!');
  console.log('');
  console.log('📋 Summary:');
  console.log(`   ✅ Backend running at: ${TEST_CONFIG.BACKEND_URL}`);
  console.log(`   ✅ Frontend running at: ${TEST_CONFIG.FRONTEND_URL}`);
  console.log(`   ✅ Global test account: ${GLOBAL_TEST_ACCOUNT.email}`);
  console.log('   ✅ Test directories created');
  console.log('   ✅ Additional test accounts created/verified');
  console.log('');
  console.log('🏃‍♂️ You can now run tests with:');
  console.log('   npx playwright test');
  console.log('   npx playwright test --headed');
  console.log('   npx playwright test tests/e2e/auth/login.test.ts');
}

// Run the verification
main().catch((error) => {
  console.error('❌ Verification failed:', error);
  process.exit(1);
});
