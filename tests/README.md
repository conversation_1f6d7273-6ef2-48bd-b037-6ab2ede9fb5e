# Test Configuration and Global Test Accounts

This directory contains test configuration files and global test accounts that can be used across all test files in the project.

## 📁 Directory Structure

```
tests/
├── config/
│   ├── test-accounts.ts      # Global test account definitions
│   └── playwright-setup.ts   # Playwright test utilities and fixtures
├── e2e/
│   └── auth/
│       ├── login.test.ts     # Login functionality tests
│       └── register.test.ts  # Registration functionality tests
├── screenshots/              # Test screenshots (auto-generated)
└── README.md                # This file
```

## 🔐 Global Test Accounts

### Primary Test Account
The main test account for general E2E testing:

```typescript
GLOBAL_TEST_ACCOUNT = {
  id: "8a68c538-d9f4-49d6-991b-d02138811c5d",
  name: "Test User Global",
  email: "<EMAIL>",
  password: "testpassword123",
  role: "staff"
}
```

### Additional Test Accounts
Additional accounts can be created for specific testing scenarios:
- `TEST_ACCOUNTS.ADMIN` - Admin user testing
- `TEST_ACCOUNTS.MANAGER` - Manager user testing  
- `TEST_ACCOUNTS.USER` - Regular user testing

## 🚀 Usage Examples

### Basic Test with Global Account

```typescript
import { test, expect } from '../config/playwright-setup';
import { GLOBAL_TEST_ACCOUNT } from '../config/test-accounts';

test('should login with global test account', async ({ page }) => {
  await page.goto('/en/login');
  await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
  await page.fill('input[name="password"]', GLOBAL_TEST_ACCOUNT.password);
  await page.click('button[type="submit"]');
  
  await page.waitForURL('**/dashboard');
});
```

### Using Authenticated Page Fixture

```typescript
import { test, expect } from '../config/playwright-setup';

test('should access dashboard when authenticated', async ({ authenticatedPage }) => {
  // authenticatedPage is already logged in with GLOBAL_TEST_ACCOUNT
  await authenticatedPage.goto('/en/dashboard');
  
  // Test authenticated functionality
  await expect(authenticatedPage.locator('h1')).toContainText('Dashboard');
});
```

### Using Test Utilities

```typescript
import { test, TestUtils, TestDataGenerator } from '../config/playwright-setup';

test('should register new user', async ({ page }) => {
  const testUser = TestDataGenerator.generateTestUser();
  
  await TestUtils.navigateAndWait(page, '/en/register');
  await TestUtils.fillAndSubmitForm(page, {
    name: testUser.name,
    email: testUser.email,
    password: testUser.password,
    confirmPassword: testUser.password
  });
  
  await TestUtils.waitForToast(page, 'success');
  await TestUtils.takeScreenshot(page, 'registration-success');
});
```

## 🛠 Test Utilities

### TestUtils Class
- `waitForPageLoad()` - Wait for page to load completely
- `takeScreenshot()` - Take timestamped screenshots
- `fillAndSubmitForm()` - Fill form fields and submit
- `waitForToast()` - Wait for toast notifications
- `isUserAuthenticated()` - Check authentication status
- `navigateAndWait()` - Navigate and wait for load
- `checkForErrors()` - Find error messages on page
- `waitForApiCall()` - Wait for specific API calls

### TestDataGenerator Class
- `generateTestEmail()` - Create unique test emails
- `generateTestUser()` - Generate test user data
- `generateTestOrganization()` - Generate test org data

### TestEnvironment Class
- `isBackendRunning()` - Check if Go backend is running
- `isFrontendRunning()` - Check if Next.js frontend is running
- `verifyEnvironment()` - Verify entire test environment

## 🔧 Configuration

### Environment Variables
Set these environment variables to customize test behavior:

```bash
# Test URLs
TEST_FRONTEND_URL=http://localhost:3001
TEST_BACKEND_URL=http://localhost:8050

# Browser settings
TEST_HEADLESS=false          # Run tests in headed mode
TEST_SLOW_MO=100            # Slow down actions by 100ms
TEST_DEVTOOLS=true          # Open browser devtools
```

### Test Configuration
The `TEST_CONFIG` object contains:
- Base URLs for frontend and backend
- Timeout settings for different operations
- Browser options for Puppeteer
- Default test data settings

## 📝 Writing Tests

### Best Practices

1. **Use Global Test Account**: Use `GLOBAL_TEST_ACCOUNT` for consistent testing
2. **Use Fixtures**: Leverage `authenticatedPage` fixture for authenticated tests
3. **Take Screenshots**: Use `TestUtils.takeScreenshot()` for visual verification
4. **Check Environment**: Always verify environment before running tests
5. **Clean Up**: Tests should clean up after themselves (logout, etc.)

### Test Structure

```typescript
test.describe('Feature Name', () => {
  test.beforeAll(async () => {
    await TestEnvironment.verifyEnvironment();
  });

  test.beforeEach(async ({ page }) => {
    // Setup for each test
  });

  test('should do something', async ({ page }) => {
    // Test implementation
  });
});
```

## 🏃‍♂️ Running Tests

### Prerequisites
1. Start the Go backend server: `cd go-backend && go run main.go`
2. Start the Next.js frontend: `npm run dev`
3. Ensure test database is set up with test accounts

### Run Tests
```bash
# Run all tests
npx playwright test

# Run specific test file
npx playwright test tests/e2e/auth/login.test.ts

# Run tests in headed mode
npx playwright test --headed

# Run tests with debug mode
npx playwright test --debug
```

## 📸 Screenshots

Test screenshots are automatically saved to `tests/screenshots/` with timestamps. Use them for:
- Visual verification of UI states
- Debugging test failures
- Documentation of test flows

## 🔍 Debugging

### Common Issues

1. **Environment Not Ready**: Ensure both frontend and backend are running
2. **Test Account Issues**: Verify test account exists in database
3. **Timing Issues**: Increase timeouts if tests are flaky
4. **Authentication Issues**: Check NextAuth configuration

### Debug Tips

1. Use `--headed` flag to see browser actions
2. Use `--debug` flag to step through tests
3. Add `await page.pause()` to pause test execution
4. Check screenshots for visual debugging
5. Use `console.log()` in tests for debugging

## 🤝 Contributing

When adding new tests:
1. Use the existing test utilities and fixtures
2. Follow the established naming conventions
3. Add appropriate screenshots for verification
4. Update this README if adding new utilities
5. Ensure tests clean up after themselves
