{"name": "adc-account-tests", "version": "1.0.0", "description": "E2E tests for ADC Account Web Application", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:auth": "playwright test tests/e2e/auth/", "test:login": "playwright test tests/e2e/auth/login.test.ts", "test:register": "playwright test tests/e2e/auth/register.test.ts", "verify-setup": "tsx verify-setup.ts", "install-playwright": "playwright install"}, "devDependencies": {"@playwright/test": "^1.40.0", "tsx": "^4.0.0"}, "keywords": ["e2e", "testing", "playwright", "<PERSON><PERSON><PERSON>", "authentication"], "author": "ADC Team", "license": "MIT"}