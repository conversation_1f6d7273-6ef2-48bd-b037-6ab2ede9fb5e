import { test, expect } from '@playwright/test';

/**
 * Backend Authentication Integration Testing
 * 
 * Tests the integration between frontend authentication and Go backend APIs.
 * Validates that authentication tokens are properly exchanged and backend
 * endpoints respond correctly to authenticated requests.
 */

const BASE_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';
const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8050';

test.describe('Backend Authentication Integration', () => {

  test.beforeEach(async ({ page }) => {
    // Ensure clean state
    await page.context().clearCookies();
  });

  test.describe('Backend Health and Connectivity', () => {
    
    test('should connect to Go backend', async ({ request }) => {
      const response = await request.get(`${BACKEND_URL}/health`);
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty('status', 'ok');
    });

    test('should handle CORS from frontend', async ({ request }) => {
      const response = await request.get(`${BACKEND_URL}/health`, {
        headers: {
          'Origin': BASE_URL
        }
      });
      
      expect(response.status()).toBe(200);
      
      const headers = response.headers();
      expect(headers['access-control-allow-origin']).toBeDefined();
    });
  });

  test.describe('Protected Endpoints', () => {
    
    test('should reject unauthenticated requests', async ({ request }) => {
      const protectedEndpoints = [
        '/api/v1/merchants',
        '/api/v1/users/me',
        '/dashboard/financial-summary'
      ];

      for (const endpoint of protectedEndpoints) {
        const response = await request.get(`${BACKEND_URL}${endpoint}`);
        expect(response.status()).toBe(401);
        
        const data = await response.json();
        expect(data).toHaveProperty('error');
        expect(data.error).toContain('Authorization');
      }
    });

    test('should reject invalid tokens', async ({ request }) => {
      const response = await request.get(`${BACKEND_URL}/api/v1/merchants`, {
        headers: {
          'Authorization': 'Bearer invalid-token'
        }
      });
      
      expect(response.status()).toBe(401);
    });

    test('should reject malformed authorization headers', async ({ request }) => {
      const malformedHeaders = [
        'invalid-format',
        'Bearer',
        'Basic invalid',
        ''
      ];

      for (const authHeader of malformedHeaders) {
        const response = await request.get(`${BACKEND_URL}/api/v1/merchants`, {
          headers: {
            'Authorization': authHeader
          }
        });
        
        expect(response.status()).toBe(401);
      }
    });
  });

  test.describe('Authentication Flow Integration', () => {
    
    test('should handle token exchange flow', async ({ page, request }) => {
      // Navigate to login page
      await page.goto(`${BASE_URL}/en/login`);
      
      // Check if we can get session info
      const sessionResponse = await request.get(`${BASE_URL}/api/auth/session`);
      expect(sessionResponse.status()).toBe(200);
      
      // Verify session structure
      const sessionData = await sessionResponse.json();
      // For unauthenticated user, should be null
      expect(sessionData).toBeNull();
    });

    test('should validate NextAuth configuration', async ({ request }) => {
      const providersResponse = await request.get(`${BASE_URL}/api/auth/providers`);
      expect(providersResponse.status()).toBe(200);
      
      const providers = await providersResponse.json();
      
      // Verify Google provider is configured
      expect(providers).toHaveProperty('google');
      expect(providers.google).toMatchObject({
        id: 'google',
        name: 'Google',
        type: 'oauth'
      });
    });
  });

  test.describe('Error Handling and Logging', () => {
    
    test('should return proper error responses', async ({ request }) => {
      const response = await request.get(`${BACKEND_URL}/api/v1/nonexistent`);
      expect(response.status()).toBe(404);
      
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });

    test('should handle server errors gracefully', async ({ request }) => {
      // Test with malformed request that might cause server error
      const response = await request.post(`${BACKEND_URL}/api/v1/merchants`, {
        data: 'invalid-json-data',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      expect([400, 401, 500]).toContain(response.status());
    });
  });

  test.describe('Security Measures', () => {
    
    test('should include security headers', async ({ request }) => {
      const response = await request.get(`${BACKEND_URL}/health`);
      
      const headers = response.headers();
      
      // Check for basic security headers
      expect(headers).toHaveProperty('x-content-type-options');
      expect(headers['x-content-type-options']).toBe('nosniff');
    });

    test('should handle rate limiting', async ({ request }) => {
      // Make multiple rapid requests to test rate limiting
      const promises = Array.from({ length: 10 }, () => 
        request.get(`${BACKEND_URL}/health`)
      );
      
      const responses = await Promise.all(promises);
      
      // All should succeed for health endpoint (usually not rate limited)
      responses.forEach(response => {
        expect([200, 429]).toContain(response.status());
      });
    });

    test('should validate input sanitization', async ({ request }) => {
      const maliciousInputs = [
        '<script>alert("xss")</script>',
        '"; DROP TABLE users; --',
        '../../../etc/passwd'
      ];

      for (const input of maliciousInputs) {
        const response = await request.post(`${BACKEND_URL}/api/v1/merchants`, {
          data: { name: input },
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        // Should reject or sanitize malicious input
        expect([400, 401, 422]).toContain(response.status());
      }
    });
  });

  test.describe('Performance and Reliability', () => {
    
    test('should meet response time requirements', async ({ request }) => {
      const endpoints = [
        '/health',
        '/api/v1/merchants' // This will return 401 but should be fast
      ];

      for (const endpoint of endpoints) {
        const startTime = Date.now();
        
        const response = await request.get(`${BACKEND_URL}${endpoint}`);
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        // Health check should be very fast, protected endpoints should respond quickly with 401
        expect(responseTime).toBeLessThan(endpoint === '/health' ? 100 : 500);
      }
    });

    test('should handle concurrent requests', async ({ request }) => {
      const promises = Array.from({ length: 5 }, () => 
        request.get(`${BACKEND_URL}/health`)
      );
      
      const responses = await Promise.all(promises);
      
      responses.forEach(response => {
        expect(response.status()).toBe(200);
      });
    });
  });
});
