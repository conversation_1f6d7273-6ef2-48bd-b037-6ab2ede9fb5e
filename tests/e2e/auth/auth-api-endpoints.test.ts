import { test, expect } from '@playwright/test';

/**
 * Authentication API Endpoints Testing Suite
 * 
 * Tests all authentication-related API endpoints to ensure proper integration
 * between frontend authentication flows and backend user management.
 * 
 * Covers:
 * - NextAuth API routes (/api/auth/[...nextauth])
 * - Login API (/api/auth/login)
 * - Register API (/api/auth/register)
 * - Session management
 * - Error handling and security
 */

const BASE_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';
const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8050';

test.describe('Authentication API Endpoints', () => {
  
  test.beforeEach(async ({ page }) => {
    // Ensure clean state for each test
    await page.context().clearCookies();
  });

  test.describe('NextAuth API Routes', () => {
    
    test('should respond to session endpoint', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/session`);
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      // Should return null for unauthenticated user
      expect(data).toBeNull();
    });

    test('should respond to providers endpoint', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/providers`);
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty('google');
      expect(data.google).toHaveProperty('id', 'google');
      expect(data.google).toHaveProperty('name', 'Google');
      expect(data.google).toHaveProperty('type', 'oauth');
    });

    test('should respond to CSRF endpoint', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/csrf`);
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty('csrfToken');
      expect(typeof data.csrfToken).toBe('string');
      expect(data.csrfToken.length).toBeGreaterThan(0);
    });

    test('should handle signin endpoint', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/signin`);
      expect(response.status()).toBe(200);
      
      const html = await response.text();
      expect(html).toContain('Sign in');
    });

    test('should handle signout endpoint', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/signout`);
      expect(response.status()).toBe(200);
      
      const html = await response.text();
      expect(html).toContain('Sign out');
    });

    test('should generate Google OAuth authorization URL', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/signin/google`);
      
      // Should redirect to Google OAuth
      expect([302, 307]).toContain(response.status());
      
      const location = response.headers()['location'];
      expect(location).toContain('accounts.google.com');
      expect(location).toContain('oauth2');
      expect(location).toContain('client_id');
    });
  });

  test.describe('Custom Authentication APIs', () => {
    
    test('should handle login endpoint', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/login`, {
        data: {
          email: '<EMAIL>',
          password: 'testpassword'
        }
      });
      
      // Should return appropriate response (401 for invalid credentials)
      expect([200, 401, 422]).toContain(response.status());
    });

    test('should handle register endpoint', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/register`, {
        data: {
          email: '<EMAIL>',
          password: 'newpassword',
          name: 'New User'
        }
      });
      
      // Should return appropriate response
      expect([200, 201, 400, 409, 422]).toContain(response.status());
    });

    test('should handle me endpoint without authentication', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/me`);
      
      // Should return 401 for unauthenticated request
      expect(response.status()).toBe(401);
    });

    test('should handle logout endpoint', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/logout`);
      
      // Should handle logout request
      expect([200, 401]).toContain(response.status());
    });
  });

  test.describe('Error Handling', () => {
    
    test('should handle invalid endpoints gracefully', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/invalid-endpoint`);
      expect(response.status()).toBe(404);
    });

    test('should validate request methods', async ({ request }) => {
      // Test invalid method on session endpoint
      const response = await request.post(`${BASE_URL}/api/auth/session`);
      expect([405, 400]).toContain(response.status());
    });

    test('should handle malformed requests', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/login`, {
        data: 'invalid-json'
      });
      
      expect([400, 422]).toContain(response.status());
    });
  });

  test.describe('Security Headers', () => {
    
    test('should include security headers', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/session`);
      
      const headers = response.headers();
      
      // Check for security headers
      expect(headers).toHaveProperty('x-frame-options');
      expect(headers).toHaveProperty('x-content-type-options');
    });

    test('should handle CORS properly', async ({ request }) => {
      const response = await request.options(`${BASE_URL}/api/auth/session`);
      
      // Should handle OPTIONS request
      expect([200, 204]).toContain(response.status());
    });
  });

  test.describe('Performance', () => {
    
    test('should respond within acceptable time limits', async ({ request }) => {
      const startTime = Date.now();
      
      const response = await request.get(`${BASE_URL}/api/auth/session`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status()).toBe(200);
      expect(responseTime).toBeLessThan(500); // Should respond within 500ms
    });

    test('should handle concurrent requests', async ({ request }) => {
      const promises = Array.from({ length: 5 }, () => 
        request.get(`${BASE_URL}/api/auth/session`)
      );
      
      const responses = await Promise.all(promises);
      
      responses.forEach(response => {
        expect(response.status()).toBe(200);
      });
    });
  });
});
