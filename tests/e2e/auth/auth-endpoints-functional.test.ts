import { test, expect } from '@playwright/test';

/**
 * Functional Authentication Endpoints Testing
 * 
 * Tests the functional behavior of authentication endpoints including
 * registration, login, session management, and user profile operations.
 */

const BASE_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';

// Test user data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  name: 'Test Auth User'
};

test.describe('Authentication Endpoints Functional Testing', () => {

  test.beforeEach(async ({ page }) => {
    // Clear any existing session
    await page.context().clearCookies();
  });

  test.describe('User Registration Flow', () => {
    
    test('should handle valid registration request', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/register`, {
        data: {
          email: `test.${Date.now()}@example.com`,
          password: testUser.password,
          name: testUser.name
        }
      });
      
      // Should either succeed or indicate user already exists
      expect([200, 201, 409]).toContain(response.status());
      
      if (response.status() === 200 || response.status() === 201) {
        const data = await response.json();
        expect(data).toHaveProperty('success');
      }
    });

    test('should validate required fields', async ({ request }) => {
      const invalidRequests = [
        { email: '', password: testUser.password, name: testUser.name },
        { email: testUser.email, password: '', name: testUser.name },
        { email: testUser.email, password: testUser.password, name: '' },
        {}
      ];

      for (const invalidData of invalidRequests) {
        const response = await request.post(`${BASE_URL}/api/auth/register`, {
          data: invalidData
        });
        
        expect([400, 422]).toContain(response.status());
      }
    });

    test('should validate email format', async ({ request }) => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'test@',
        'test.example.com'
      ];

      for (const email of invalidEmails) {
        const response = await request.post(`${BASE_URL}/api/auth/register`, {
          data: {
            email,
            password: testUser.password,
            name: testUser.name
          }
        });
        
        expect([400, 422]).toContain(response.status());
      }
    });

    test('should validate password strength', async ({ request }) => {
      const weakPasswords = [
        '123',
        'password',
        'abc',
        ''
      ];

      for (const password of weakPasswords) {
        const response = await request.post(`${BASE_URL}/api/auth/register`, {
          data: {
            email: `test.${Date.now()}@example.com`,
            password,
            name: testUser.name
          }
        });
        
        expect([400, 422]).toContain(response.status());
      }
    });
  });

  test.describe('User Login Flow', () => {
    
    test('should handle login request', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/login`, {
        data: {
          email: '<EMAIL>', // Use existing test user
          password: 'testpassword'
        }
      });
      
      // Should either succeed or return appropriate error
      expect([200, 401, 422]).toContain(response.status());
    });

    test('should reject invalid credentials', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/login`, {
        data: {
          email: '<EMAIL>',
          password: 'wrongpassword'
        }
      });
      
      expect(response.status()).toBe(401);
      
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });

    test('should validate login input', async ({ request }) => {
      const invalidRequests = [
        { email: '', password: 'password' },
        { email: '<EMAIL>', password: '' },
        {}
      ];

      for (const invalidData of invalidRequests) {
        const response = await request.post(`${BASE_URL}/api/auth/login`, {
          data: invalidData
        });
        
        expect([400, 422]).toContain(response.status());
      }
    });
  });

  test.describe('Session Management', () => {
    
    test('should return null session for unauthenticated user', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/session`);
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data).toBeNull();
    });

    test('should handle session with cookies', async ({ page, request }) => {
      // Navigate to login page to establish session context
      await page.goto(`${BASE_URL}/en/login`);
      
      // Get session using page context (which includes cookies)
      const response = await page.request.get(`${BASE_URL}/api/auth/session`);
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      // Should be null for unauthenticated user
      expect(data).toBeNull();
    });

    test('should handle CSRF token generation', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/csrf`);
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty('csrfToken');
      expect(typeof data.csrfToken).toBe('string');
      expect(data.csrfToken.length).toBeGreaterThan(10);
    });
  });

  test.describe('User Profile Endpoints', () => {
    
    test('should require authentication for profile access', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/me`);
      expect(response.status()).toBe(401);
    });

    test('should handle logout request', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/logout`);
      
      // Should handle logout (may return 200 even if not logged in)
      expect([200, 401]).toContain(response.status());
    });

    test('should handle signout request', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/signout`);
      
      // Should handle signout request
      expect([200, 302, 401]).toContain(response.status());
    });
  });

  test.describe('OAuth Provider Integration', () => {
    
    test('should provide Google OAuth configuration', async ({ request }) => {
      const response = await request.get(`${BASE_URL}/api/auth/providers`);
      expect(response.status()).toBe(200);
      
      const providers = await response.json();
      
      expect(providers).toHaveProperty('google');
      expect(providers.google).toMatchObject({
        id: 'google',
        name: 'Google',
        type: 'oauth'
      });
    });

    test('should handle Google signin initiation', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/signin/google`);
      
      // Should redirect to Google OAuth
      expect([302, 307]).toContain(response.status());
      
      const location = response.headers()['location'];
      if (location) {
        expect(location).toContain('accounts.google.com');
        expect(location).toContain('oauth2');
      }
    });

    test('should handle OAuth callback structure', async ({ request }) => {
      // Test the callback endpoint structure (without actual OAuth flow)
      const response = await request.get(`${BASE_URL}/api/auth/callback/google`);
      
      // Should handle callback request (may redirect or return error)
      expect([200, 302, 400, 401]).toContain(response.status());
    });
  });

  test.describe('Error Handling and Edge Cases', () => {
    
    test('should handle malformed JSON requests', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/login`, {
        data: 'invalid-json',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      expect([400, 422]).toContain(response.status());
    });

    test('should handle missing Content-Type header', async ({ request }) => {
      const response = await request.post(`${BASE_URL}/api/auth/login`, {
        data: JSON.stringify({
          email: '<EMAIL>',
          password: 'password'
        })
      });
      
      // Should handle request even without explicit Content-Type
      expect([200, 400, 401, 422]).toContain(response.status());
    });

    test('should handle oversized requests', async ({ request }) => {
      const largeData = {
        email: '<EMAIL>',
        password: 'password',
        name: 'A'.repeat(10000) // Very long name
      };

      const response = await request.post(`${BASE_URL}/api/auth/register`, {
        data: largeData
      });
      
      expect([400, 413, 422]).toContain(response.status());
    });
  });
});
