/**
 * Login E2E Tests
 * 
 * Tests the login functionality using the global test account
 */

import { test, expect, TestUtils, TestEnvironment } from '../../config/playwright-setup';
import { GLOBAL_TEST_ACCOUNT, TEST_CONFIG } from '../../config/test-accounts';

test.describe('Login Flow', () => {
  test.beforeAll(async () => {
    // Verify test environment is ready
    await TestEnvironment.verifyEnvironment();
  });

  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await TestUtils.navigateAndWait(page, `${TEST_CONFIG.FRONTEND_URL}/en/login`);
  });

  test('should display login form correctly', async ({ page }) => {
    // Check if all form elements are present
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // Check for Google OAuth button
    await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
    
    // Check for register link
    await expect(page.locator('a[href*="/register"]')).toBeVisible();
    
    // Take screenshot for verification
    await TestUtils.takeScreenshot(page, 'login-form-display');
  });

  test('should successfully login with global test account', async ({ page }) => {
    // Login with global test account
    await TestUtils.fillAndSubmitForm(page, {
      email: GLOBAL_TEST_ACCOUNT.email,
      password: GLOBAL_TEST_ACCOUNT.password
    });

    // Wait for redirect to dashboard
    await page.waitForURL('**/dashboard', { timeout: TEST_CONFIG.NAVIGATION_TIMEOUT });
    
    // Verify user is authenticated
    const isAuthenticated = await TestUtils.isUserAuthenticated(page);
    expect(isAuthenticated).toBe(true);
    
    // Take screenshot of successful login
    await TestUtils.takeScreenshot(page, 'login-success-dashboard');
  });

  test('should show error for invalid credentials', async ({ page }) => {
    // Try to login with invalid credentials
    await TestUtils.fillAndSubmitForm(page, {
      email: GLOBAL_TEST_ACCOUNT.email,
      password: 'wrongpassword'
    });

    // Wait for error message
    await TestUtils.waitForToast(page);
    
    // Verify we're still on login page
    expect(page.url()).toContain('/login');
    
    // Take screenshot of error state
    await TestUtils.takeScreenshot(page, 'login-invalid-credentials-error');
  });

  test('should show error for non-existent user', async ({ page }) => {
    // Try to login with non-existent email
    await TestUtils.fillAndSubmitForm(page, {
      email: '<EMAIL>',
      password: 'anypassword'
    });

    // Wait for error message
    await TestUtils.waitForToast(page);
    
    // Verify we're still on login page
    expect(page.url()).toContain('/login');
    
    // Take screenshot of error state
    await TestUtils.takeScreenshot(page, 'login-nonexistent-user-error');
  });

  test('should handle form validation correctly', async ({ page }) => {
    // Test empty form submission
    await page.click('button[type="submit"]');
    
    // Check for validation errors
    const errors = await TestUtils.checkForErrors(page);
    expect(errors.length).toBeGreaterThan(0);
    
    // Test invalid email format
    await TestUtils.fillAndSubmitForm(page, {
      email: 'invalid-email',
      password: 'password123'
    });
    
    // Should show email validation error
    await TestUtils.waitForToast(page);
    
    // Take screenshot of validation errors
    await TestUtils.takeScreenshot(page, 'login-form-validation');
  });

  test('should redirect authenticated user to dashboard', async ({ authenticatedPage }) => {
    // Try to access login page while authenticated
    await authenticatedPage.goto(`${TEST_CONFIG.FRONTEND_URL}/en/login`);
    
    // Should be redirected to dashboard
    await authenticatedPage.waitForURL('**/dashboard', { timeout: TEST_CONFIG.NAVIGATION_TIMEOUT });
    
    // Take screenshot
    await TestUtils.takeScreenshot(authenticatedPage, 'login-authenticated-redirect');
  });

  test('should maintain form state during validation errors', async ({ page }) => {
    // Fill form with invalid data
    await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
    await page.fill('input[name="password"]', 'wrongpassword');
    
    await page.click('button[type="submit"]');
    
    // Wait for error
    await TestUtils.waitForToast(page);
    
    // Verify email is maintained (password should be cleared for security)
    expect(await page.inputValue('input[name="email"]')).toBe(GLOBAL_TEST_ACCOUNT.email);
    
    // Take screenshot
    await TestUtils.takeScreenshot(page, 'login-form-state-maintained');
  });

  test('should have working navigation links', async ({ page }) => {
    // Check for register link
    const registerLink = page.locator('a[href*="/register"]');
    await expect(registerLink).toBeVisible();
    
    // Click register link and verify navigation
    await registerLink.click();
    await page.waitForURL('**/register', { timeout: TEST_CONFIG.NAVIGATION_TIMEOUT });
    
    // Take screenshot
    await TestUtils.takeScreenshot(page, 'login-navigation-to-register');
  });

  test('should handle loading states correctly', async ({ page }) => {
    // Fill form
    await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
    await page.fill('input[name="password"]', GLOBAL_TEST_ACCOUNT.password);
    
    // Click submit and immediately check for loading state
    await page.click('button[type="submit"]');
    
    // Check if loading indicator appears
    const loadingIndicator = page.locator('.animate-spin, [data-loading="true"]');
    
    // Take screenshot of loading state (if it appears)
    try {
      await loadingIndicator.waitFor({ timeout: 1000 });
      await TestUtils.takeScreenshot(page, 'login-loading-state');
    } catch {
      // Loading state might be too fast to capture
      console.log('Loading state was too fast to capture');
    }
    
    // Wait for completion
    await page.waitForURL('**/dashboard', { timeout: TEST_CONFIG.NAVIGATION_TIMEOUT });
  });

  test('should support keyboard navigation', async ({ page }) => {
    // Test tab navigation
    await page.keyboard.press('Tab'); // Should focus email field
    await expect(page.locator('input[name="email"]')).toBeFocused();
    
    await page.keyboard.press('Tab'); // Should focus password field
    await expect(page.locator('input[name="password"]')).toBeFocused();
    
    await page.keyboard.press('Tab'); // Should focus submit button
    await expect(page.locator('button[type="submit"]')).toBeFocused();
    
    // Test form submission with Enter key
    await page.fill('input[name="email"]', GLOBAL_TEST_ACCOUNT.email);
    await page.fill('input[name="password"]', GLOBAL_TEST_ACCOUNT.password);
    
    // Focus email field and press Enter
    await page.focus('input[name="email"]');
    await page.keyboard.press('Enter');
    
    // Should submit form and redirect
    await page.waitForURL('**/dashboard', { timeout: TEST_CONFIG.NAVIGATION_TIMEOUT });
    
    // Take screenshot
    await TestUtils.takeScreenshot(page, 'login-keyboard-navigation');
  });
});
