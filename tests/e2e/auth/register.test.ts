/**
 * Registration E2E Tests
 * 
 * Tests the registration functionality using NextAuth integration
 */

import { test, expect, TestUtils, TestDataGenerator, TestEnvironment } from '../../config/playwright-setup';
import { GLOBAL_TEST_ACCOUNT, TEST_CONFIG } from '../../config/test-accounts';

test.describe('Registration Flow', () => {
  test.beforeAll(async () => {
    // Verify test environment is ready
    await TestEnvironment.verifyEnvironment();
  });

  test.beforeEach(async ({ page }) => {
    // Navigate to register page
    await TestUtils.navigateAndWait(page, `${TEST_CONFIG.FRONTEND_URL}/en/register`);
  });

  test('should display registration form correctly', async ({ page }) => {
    // Check if all form elements are present
    await expect(page.locator('input[name="name"]')).toBeVisible();
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('input[name="confirmPassword"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // Check for Google OAuth button
    await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
    
    // Take screenshot for verification
    await TestUtils.takeScreenshot(page, 'register-form-display');
  });

  test('should show validation error for password mismatch', async ({ page }) => {
    const testUser = TestDataGenerator.generateTestUser();
    
    // Fill form with mismatched passwords
    await TestUtils.fillAndSubmitForm(page, {
      name: testUser.name,
      email: testUser.email,
      password: testUser.password,
      confirmPassword: 'differentpassword'
    });

    // Wait for error toast
    await TestUtils.waitForToast(page, 'passwordMismatch');
    
    // Take screenshot of error state
    await TestUtils.takeScreenshot(page, 'register-password-mismatch-error');
  });

  test('should successfully register new user with NextAuth', async ({ page }) => {
    const testUser = TestDataGenerator.generateTestUser({
      name: 'New Test User',
      email: TestDataGenerator.generateTestEmail('newuser')
    });
    
    // Fill registration form
    await TestUtils.fillAndSubmitForm(page, {
      name: testUser.name,
      email: testUser.email,
      password: testUser.password,
      confirmPassword: testUser.password
    });

    // Wait for successful registration and redirect to dashboard
    await page.waitForURL('**/dashboard', { timeout: TEST_CONFIG.NAVIGATION_TIMEOUT });
    
    // Verify user is authenticated
    const isAuthenticated = await TestUtils.isUserAuthenticated(page);
    expect(isAuthenticated).toBe(true);
    
    // Take screenshot of successful registration
    await TestUtils.takeScreenshot(page, 'register-success-dashboard');
  });

  test('should show error for existing user email', async ({ page }) => {
    // Try to register with existing global test account email
    await TestUtils.fillAndSubmitForm(page, {
      name: 'Another User',
      email: GLOBAL_TEST_ACCOUNT.email, // This email already exists
      password: 'newpassword123',
      confirmPassword: 'newpassword123'
    });

    // Wait for error message
    await TestUtils.waitForToast(page, 'already exists');
    
    // Verify we're still on register page
    expect(page.url()).toContain('/register');
    
    // Take screenshot of error state
    await TestUtils.takeScreenshot(page, 'register-existing-email-error');
  });

  test('should redirect authenticated user to dashboard', async ({ authenticatedPage }) => {
    // Try to access register page while authenticated
    await authenticatedPage.goto(`${TEST_CONFIG.FRONTEND_URL}/en/register`);
    
    // Should be redirected to dashboard
    await authenticatedPage.waitForURL('**/dashboard', { timeout: TEST_CONFIG.NAVIGATION_TIMEOUT });
    
    // Take screenshot
    await TestUtils.takeScreenshot(authenticatedPage, 'register-authenticated-redirect');
  });

  test('should handle form validation correctly', async ({ page }) => {
    // Test empty form submission
    await page.click('button[type="submit"]');
    
    // Check for validation errors
    const errors = await TestUtils.checkForErrors(page);
    expect(errors.length).toBeGreaterThan(0);
    
    // Test invalid email format
    await TestUtils.fillAndSubmitForm(page, {
      name: 'Test User',
      email: 'invalid-email',
      password: 'password123',
      confirmPassword: 'password123'
    });
    
    // Should show email validation error
    await TestUtils.waitForToast(page);
    
    // Take screenshot of validation errors
    await TestUtils.takeScreenshot(page, 'register-form-validation');
  });

  test('should maintain form state during validation errors', async ({ page }) => {
    const testUser = TestDataGenerator.generateTestUser();
    
    // Fill form with mismatched passwords
    await page.fill('input[name="name"]', testUser.name);
    await page.fill('input[name="email"]', testUser.email);
    await page.fill('input[name="password"]', testUser.password);
    await page.fill('input[name="confirmPassword"]', 'wrongpassword');
    
    await page.click('button[type="submit"]');
    
    // Wait for error
    await TestUtils.waitForToast(page);
    
    // Verify form values are maintained
    expect(await page.inputValue('input[name="name"]')).toBe(testUser.name);
    expect(await page.inputValue('input[name="email"]')).toBe(testUser.email);
    expect(await page.inputValue('input[name="password"]')).toBe(testUser.password);
    
    // Take screenshot
    await TestUtils.takeScreenshot(page, 'register-form-state-maintained');
  });

  test('should have working navigation links', async ({ page }) => {
    // Check for login link
    const loginLink = page.locator('a[href*="/login"]');
    await expect(loginLink).toBeVisible();
    
    // Click login link and verify navigation
    await loginLink.click();
    await page.waitForURL('**/login', { timeout: TEST_CONFIG.NAVIGATION_TIMEOUT });
    
    // Take screenshot
    await TestUtils.takeScreenshot(page, 'register-navigation-to-login');
  });
});
