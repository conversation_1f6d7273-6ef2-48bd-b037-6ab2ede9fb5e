# Test info

- Name: Backend Authentication Integration >> Error Handling and Logging >> should handle server errors gracefully
- Location: /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:136:9

# Error details

```
Error: expect(received).toContain(expected) // indexOf

Expected value: 404
Received array: [400, 401, 500]
    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:145:31
```

# Test source

```ts
   45 |   test.describe('Protected Endpoints', () => {
   46 |     
   47 |     test('should reject unauthenticated requests', async ({ request }) => {
   48 |       const protectedEndpoints = [
   49 |         '/api/v1/merchants',
   50 |         '/api/v1/users/me',
   51 |         '/dashboard/financial-summary'
   52 |       ];
   53 |
   54 |       for (const endpoint of protectedEndpoints) {
   55 |         const response = await request.get(`${BACKEND_URL}${endpoint}`);
   56 |         expect(response.status()).toBe(401);
   57 |         
   58 |         const data = await response.json();
   59 |         expect(data).toHaveProperty('error');
   60 |         expect(data.error).toContain('Authorization');
   61 |       }
   62 |     });
   63 |
   64 |     test('should reject invalid tokens', async ({ request }) => {
   65 |       const response = await request.get(`${BACKEND_URL}/api/v1/merchants`, {
   66 |         headers: {
   67 |           'Authorization': 'Bearer invalid-token'
   68 |         }
   69 |       });
   70 |       
   71 |       expect(response.status()).toBe(401);
   72 |     });
   73 |
   74 |     test('should reject malformed authorization headers', async ({ request }) => {
   75 |       const malformedHeaders = [
   76 |         'invalid-format',
   77 |         'Bearer',
   78 |         'Basic invalid',
   79 |         ''
   80 |       ];
   81 |
   82 |       for (const authHeader of malformedHeaders) {
   83 |         const response = await request.get(`${BACKEND_URL}/api/v1/merchants`, {
   84 |           headers: {
   85 |             'Authorization': authHeader
   86 |           }
   87 |         });
   88 |         
   89 |         expect(response.status()).toBe(401);
   90 |       }
   91 |     });
   92 |   });
   93 |
   94 |   test.describe('Authentication Flow Integration', () => {
   95 |     
   96 |     test('should handle token exchange flow', async ({ page, request }) => {
   97 |       // Navigate to login page
   98 |       await page.goto(`${BASE_URL}/en/login`);
   99 |       
  100 |       // Check if we can get session info
  101 |       const sessionResponse = await request.get(`${BASE_URL}/api/auth/session`);
  102 |       expect(sessionResponse.status()).toBe(200);
  103 |       
  104 |       // Verify session structure
  105 |       const sessionData = await sessionResponse.json();
  106 |       // For unauthenticated user, should be null
  107 |       expect(sessionData).toBeNull();
  108 |     });
  109 |
  110 |     test('should validate NextAuth configuration', async ({ request }) => {
  111 |       const providersResponse = await request.get(`${BASE_URL}/api/auth/providers`);
  112 |       expect(providersResponse.status()).toBe(200);
  113 |       
  114 |       const providers = await providersResponse.json();
  115 |       
  116 |       // Verify Google provider is configured
  117 |       expect(providers).toHaveProperty('google');
  118 |       expect(providers.google).toMatchObject({
  119 |         id: 'google',
  120 |         name: 'Google',
  121 |         type: 'oauth'
  122 |       });
  123 |     });
  124 |   });
  125 |
  126 |   test.describe('Error Handling and Logging', () => {
  127 |     
  128 |     test('should return proper error responses', async ({ request }) => {
  129 |       const response = await request.get(`${BACKEND_URL}/api/v1/nonexistent`);
  130 |       expect(response.status()).toBe(404);
  131 |       
  132 |       const data = await response.json();
  133 |       expect(data).toHaveProperty('error');
  134 |     });
  135 |
  136 |     test('should handle server errors gracefully', async ({ request }) => {
  137 |       // Test with malformed request that might cause server error
  138 |       const response = await request.post(`${BACKEND_URL}/api/v1/merchants`, {
  139 |         data: 'invalid-json-data',
  140 |         headers: {
  141 |           'Content-Type': 'application/json'
  142 |         }
  143 |       });
  144 |       
> 145 |       expect([400, 401, 500]).toContain(response.status());
      |                               ^ Error: expect(received).toContain(expected) // indexOf
  146 |     });
  147 |   });
  148 |
  149 |   test.describe('Security Measures', () => {
  150 |     
  151 |     test('should include security headers', async ({ request }) => {
  152 |       const response = await request.get(`${BACKEND_URL}/health`);
  153 |       
  154 |       const headers = response.headers();
  155 |       
  156 |       // Check for basic security headers
  157 |       expect(headers).toHaveProperty('x-content-type-options');
  158 |       expect(headers['x-content-type-options']).toBe('nosniff');
  159 |     });
  160 |
  161 |     test('should handle rate limiting', async ({ request }) => {
  162 |       // Make multiple rapid requests to test rate limiting
  163 |       const promises = Array.from({ length: 10 }, () => 
  164 |         request.get(`${BACKEND_URL}/health`)
  165 |       );
  166 |       
  167 |       const responses = await Promise.all(promises);
  168 |       
  169 |       // All should succeed for health endpoint (usually not rate limited)
  170 |       responses.forEach(response => {
  171 |         expect([200, 429]).toContain(response.status());
  172 |       });
  173 |     });
  174 |
  175 |     test('should validate input sanitization', async ({ request }) => {
  176 |       const maliciousInputs = [
  177 |         '<script>alert("xss")</script>',
  178 |         '"; DROP TABLE users; --',
  179 |         '../../../etc/passwd'
  180 |       ];
  181 |
  182 |       for (const input of maliciousInputs) {
  183 |         const response = await request.post(`${BACKEND_URL}/api/v1/merchants`, {
  184 |           data: { name: input },
  185 |           headers: {
  186 |             'Content-Type': 'application/json'
  187 |           }
  188 |         });
  189 |         
  190 |         // Should reject or sanitize malicious input
  191 |         expect([400, 401, 422]).toContain(response.status());
  192 |       }
  193 |     });
  194 |   });
  195 |
  196 |   test.describe('Performance and Reliability', () => {
  197 |     
  198 |     test('should meet response time requirements', async ({ request }) => {
  199 |       const endpoints = [
  200 |         '/health',
  201 |         '/api/v1/merchants' // This will return 401 but should be fast
  202 |       ];
  203 |
  204 |       for (const endpoint of endpoints) {
  205 |         const startTime = Date.now();
  206 |         
  207 |         const response = await request.get(`${BACKEND_URL}${endpoint}`);
  208 |         
  209 |         const endTime = Date.now();
  210 |         const responseTime = endTime - startTime;
  211 |         
  212 |         // Health check should be very fast, protected endpoints should respond quickly with 401
  213 |         expect(responseTime).toBeLessThan(endpoint === '/health' ? 100 : 500);
  214 |       }
  215 |     });
  216 |
  217 |     test('should handle concurrent requests', async ({ request }) => {
  218 |       const promises = Array.from({ length: 5 }, () => 
  219 |         request.get(`${BACKEND_URL}/health`)
  220 |       );
  221 |       
  222 |       const responses = await Promise.all(promises);
  223 |       
  224 |       responses.forEach(response => {
  225 |         expect(response.status()).toBe(200);
  226 |       });
  227 |     });
  228 |   });
  229 | });
  230 |
```