# Test info

- Name: Backend Authentication Integration >> Protected Endpoints >> should reject unauthenticated requests
- Location: /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:47:9

# Error details

```
Error: expect(received).toBe(expected) // Object.is equality

Expected: 401
Received: 404
    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:56:35
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | /**
   4 |  * Backend Authentication Integration Testing
   5 |  * 
   6 |  * Tests the integration between frontend authentication and Go backend APIs.
   7 |  * Validates that authentication tokens are properly exchanged and backend
   8 |  * endpoints respond correctly to authenticated requests.
   9 |  */
   10 |
   11 | const BASE_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';
   12 | const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8050';
   13 |
   14 | test.describe('Backend Authentication Integration', () => {
   15 |
   16 |   test.beforeEach(async ({ page }) => {
   17 |     // Ensure clean state
   18 |     await page.context().clearCookies();
   19 |   });
   20 |
   21 |   test.describe('Backend Health and Connectivity', () => {
   22 |     
   23 |     test('should connect to Go backend', async ({ request }) => {
   24 |       const response = await request.get(`${BACKEND_URL}/health`);
   25 |       expect(response.status()).toBe(200);
   26 |       
   27 |       const data = await response.json();
   28 |       expect(data).toHaveProperty('status', 'ok');
   29 |     });
   30 |
   31 |     test('should handle CORS from frontend', async ({ request }) => {
   32 |       const response = await request.get(`${BACKEND_URL}/health`, {
   33 |         headers: {
   34 |           'Origin': BASE_URL
   35 |         }
   36 |       });
   37 |       
   38 |       expect(response.status()).toBe(200);
   39 |       
   40 |       const headers = response.headers();
   41 |       expect(headers['access-control-allow-origin']).toBeDefined();
   42 |     });
   43 |   });
   44 |
   45 |   test.describe('Protected Endpoints', () => {
   46 |     
   47 |     test('should reject unauthenticated requests', async ({ request }) => {
   48 |       const protectedEndpoints = [
   49 |         '/api/v1/merchants',
   50 |         '/api/v1/users/me',
   51 |         '/dashboard/financial-summary'
   52 |       ];
   53 |
   54 |       for (const endpoint of protectedEndpoints) {
   55 |         const response = await request.get(`${BACKEND_URL}${endpoint}`);
>  56 |         expect(response.status()).toBe(401);
      |                                   ^ Error: expect(received).toBe(expected) // Object.is equality
   57 |         
   58 |         const data = await response.json();
   59 |         expect(data).toHaveProperty('error');
   60 |         expect(data.error).toContain('Authorization');
   61 |       }
   62 |     });
   63 |
   64 |     test('should reject invalid tokens', async ({ request }) => {
   65 |       const response = await request.get(`${BACKEND_URL}/api/v1/merchants`, {
   66 |         headers: {
   67 |           'Authorization': 'Bearer invalid-token'
   68 |         }
   69 |       });
   70 |       
   71 |       expect(response.status()).toBe(401);
   72 |     });
   73 |
   74 |     test('should reject malformed authorization headers', async ({ request }) => {
   75 |       const malformedHeaders = [
   76 |         'invalid-format',
   77 |         'Bearer',
   78 |         'Basic invalid',
   79 |         ''
   80 |       ];
   81 |
   82 |       for (const authHeader of malformedHeaders) {
   83 |         const response = await request.get(`${BACKEND_URL}/api/v1/merchants`, {
   84 |           headers: {
   85 |             'Authorization': authHeader
   86 |           }
   87 |         });
   88 |         
   89 |         expect(response.status()).toBe(401);
   90 |       }
   91 |     });
   92 |   });
   93 |
   94 |   test.describe('Authentication Flow Integration', () => {
   95 |     
   96 |     test('should handle token exchange flow', async ({ page, request }) => {
   97 |       // Navigate to login page
   98 |       await page.goto(`${BASE_URL}/en/login`);
   99 |       
  100 |       // Check if we can get session info
  101 |       const sessionResponse = await request.get(`${BASE_URL}/api/auth/session`);
  102 |       expect(sessionResponse.status()).toBe(200);
  103 |       
  104 |       // Verify session structure
  105 |       const sessionData = await sessionResponse.json();
  106 |       // For unauthenticated user, should be null
  107 |       expect(sessionData).toBeNull();
  108 |     });
  109 |
  110 |     test('should validate NextAuth configuration', async ({ request }) => {
  111 |       const providersResponse = await request.get(`${BASE_URL}/api/auth/providers`);
  112 |       expect(providersResponse.status()).toBe(200);
  113 |       
  114 |       const providers = await providersResponse.json();
  115 |       
  116 |       // Verify Google provider is configured
  117 |       expect(providers).toHaveProperty('google');
  118 |       expect(providers.google).toMatchObject({
  119 |         id: 'google',
  120 |         name: 'Google',
  121 |         type: 'oauth'
  122 |       });
  123 |     });
  124 |   });
  125 |
  126 |   test.describe('Error Handling and Logging', () => {
  127 |     
  128 |     test('should return proper error responses', async ({ request }) => {
  129 |       const response = await request.get(`${BACKEND_URL}/api/v1/nonexistent`);
  130 |       expect(response.status()).toBe(404);
  131 |       
  132 |       const data = await response.json();
  133 |       expect(data).toHaveProperty('error');
  134 |     });
  135 |
  136 |     test('should handle server errors gracefully', async ({ request }) => {
  137 |       // Test with malformed request that might cause server error
  138 |       const response = await request.post(`${BACKEND_URL}/api/v1/merchants`, {
  139 |         data: 'invalid-json-data',
  140 |         headers: {
  141 |           'Content-Type': 'application/json'
  142 |         }
  143 |       });
  144 |       
  145 |       expect([400, 401, 500]).toContain(response.status());
  146 |     });
  147 |   });
  148 |
  149 |   test.describe('Security Measures', () => {
  150 |     
  151 |     test('should include security headers', async ({ request }) => {
  152 |       const response = await request.get(`${BACKEND_URL}/health`);
  153 |       
  154 |       const headers = response.headers();
  155 |       
  156 |       // Check for basic security headers
```