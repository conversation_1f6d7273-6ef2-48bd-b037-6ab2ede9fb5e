{"config": {"configFile": "/Users/<USER>/Desktop/adc/adc-account-web/tests/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Desktop/adc/adc-account-web/tests/config/global-setup.ts", "globalTeardown": "/Users/<USER>/Desktop/adc/adc-account-web/tests/config/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 4, "webServer": null}, "suites": [{"title": "auth/backend-integration.test.ts", "file": "auth/backend-integration.test.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Backend Authentication Integration", "file": "auth/backend-integration.test.ts", "line": 14, "column": 6, "specs": [], "suites": [{"title": "Backend Health and Connectivity", "file": "auth/backend-integration.test.ts", "line": 21, "column": 8, "specs": [{"title": "should connect to Go backend", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2340, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:33.132Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a8af240ccd937c26ef72-f08c0d4908644cadbf48", "file": "auth/backend-integration.test.ts", "line": 23, "column": 9}, {"title": "should handle CORS from frontend", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 2366, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:33.135Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a8af240ccd937c26ef72-97d82564fd3e89f1f560", "file": "auth/backend-integration.test.ts", "line": 31, "column": 9}]}, {"title": "Protected Endpoints", "file": "auth/backend-integration.test.ts", "line": 45, "column": 8, "specs": [{"title": "should reject unauthenticated requests", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 2360, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m401\u001b[39m\nReceived: \u001b[31m404\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m401\u001b[39m\nReceived: \u001b[31m404\u001b[39m\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:56:35", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 35, "line": 56}, "snippet": "\u001b[0m \u001b[90m 54 |\u001b[39m       \u001b[36mfor\u001b[39m (\u001b[36mconst\u001b[39m endpoint \u001b[36mof\u001b[39m protectedEndpoints) {\n \u001b[90m 55 |\u001b[39m         \u001b[36mconst\u001b[39m response \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m request\u001b[33m.\u001b[39m\u001b[36mget\u001b[39m(\u001b[32m`${BACKEND_URL}${endpoint}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 56 |\u001b[39m         expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m401\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 57 |\u001b[39m         \n \u001b[90m 58 |\u001b[39m         \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m response\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'error'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 35, "line": 56}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m401\u001b[39m\nReceived: \u001b[31m404\u001b[39m\n\n\u001b[0m \u001b[90m 54 |\u001b[39m       \u001b[36mfor\u001b[39m (\u001b[36mconst\u001b[39m endpoint \u001b[36mof\u001b[39m protectedEndpoints) {\n \u001b[90m 55 |\u001b[39m         \u001b[36mconst\u001b[39m response \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m request\u001b[33m.\u001b[39m\u001b[36mget\u001b[39m(\u001b[32m`${BACKEND_URL}${endpoint}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 56 |\u001b[39m         expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m401\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 57 |\u001b[39m         \n \u001b[90m 58 |\u001b[39m         \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m response\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'error'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:56:35\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:33.144Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-84a5a-ct-unauthenticated-requests-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-84a5a-ct-unauthenticated-requests-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-84a5a-ct-unauthenticated-requests-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 35, "line": 56}}], "status": "unexpected"}], "id": "a8af240ccd937c26ef72-66ba27931912e2df652e", "file": "auth/backend-integration.test.ts", "line": 47, "column": 9}, {"title": "should reject invalid tokens", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 2382, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m401\u001b[39m\nReceived: \u001b[31m404\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m401\u001b[39m\nReceived: \u001b[31m404\u001b[39m\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:71:33", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 33, "line": 71}, "snippet": "\u001b[0m \u001b[90m 69 |\u001b[39m       })\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m       \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 71 |\u001b[39m       expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m401\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 72 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 73 |\u001b[39m\n \u001b[90m 74 |\u001b[39m     test(\u001b[32m'should reject malformed authorization headers'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ request }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 33, "line": 71}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m401\u001b[39m\nReceived: \u001b[31m404\u001b[39m\n\n\u001b[0m \u001b[90m 69 |\u001b[39m       })\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m       \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 71 |\u001b[39m       expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m401\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 72 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 73 |\u001b[39m\n \u001b[90m 74 |\u001b[39m     test(\u001b[32m'should reject malformed authorization headers'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ request }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:71:33\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:33.134Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-f89d7-hould-reject-invalid-tokens-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-f89d7-hould-reject-invalid-tokens-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-f89d7-hould-reject-invalid-tokens-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 33, "line": 71}}], "status": "unexpected"}], "id": "a8af240ccd937c26ef72-7ef52247b42d055991a7", "file": "auth/backend-integration.test.ts", "line": 64, "column": 9}, {"title": "should reject malformed authorization headers", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 697, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m401\u001b[39m\nReceived: \u001b[31m404\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m401\u001b[39m\nReceived: \u001b[31m404\u001b[39m\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:89:35", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 35, "line": 89}, "snippet": "\u001b[0m \u001b[90m 87 |\u001b[39m         })\u001b[33m;\u001b[39m\n \u001b[90m 88 |\u001b[39m         \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 89 |\u001b[39m         expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m401\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 90 |\u001b[39m       }\n \u001b[90m 91 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 92 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 35, "line": 89}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m401\u001b[39m\nReceived: \u001b[31m404\u001b[39m\n\n\u001b[0m \u001b[90m 87 |\u001b[39m         })\u001b[33m;\u001b[39m\n \u001b[90m 88 |\u001b[39m         \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 89 |\u001b[39m         expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m401\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 90 |\u001b[39m       }\n \u001b[90m 91 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 92 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:89:35\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:36.382Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-62a9a-ormed-authorization-headers-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-62a9a-ormed-authorization-headers-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-62a9a-ormed-authorization-headers-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 35, "line": 89}}], "status": "unexpected"}], "id": "a8af240ccd937c26ef72-1bdbad86f852a412103b", "file": "auth/backend-integration.test.ts", "line": 74, "column": 9}]}, {"title": "Authentication Flow Integration", "file": "auth/backend-integration.test.ts", "line": 94, "column": 8, "specs": [{"title": "should handle token exchange flow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 2624, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeNull\u001b[2m()\u001b[22m\n\nReceived: \u001b[31m{}\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeNull\u001b[2m()\u001b[22m\n\nReceived: \u001b[31m{}\u001b[39m\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:107:27", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 27, "line": 107}, "snippet": "\u001b[0m \u001b[90m 105 |\u001b[39m       \u001b[36mconst\u001b[39m sessionData \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m sessionResponse\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 106 |\u001b[39m       \u001b[90m// For unauthenticated user, should be null\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 107 |\u001b[39m       expect(sessionData)\u001b[33m.\u001b[39mtoBeNull()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 108 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     test(\u001b[32m'should validate NextAuth configuration'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ request }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 27, "line": 107}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeNull\u001b[2m()\u001b[22m\n\nReceived: \u001b[31m{}\u001b[39m\n\n\u001b[0m \u001b[90m 105 |\u001b[39m       \u001b[36mconst\u001b[39m sessionData \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m sessionResponse\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 106 |\u001b[39m       \u001b[90m// For unauthenticated user, should be null\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 107 |\u001b[39m       expect(sessionData)\u001b[33m.\u001b[39mtoBeNull()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 108 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     test(\u001b[32m'should validate NextAuth configuration'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ request }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:107:27\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:36.415Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-28c85--handle-token-exchange-flow-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-28c85--handle-token-exchange-flow-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-28c85--handle-token-exchange-flow-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 27, "line": 107}}], "status": "unexpected"}], "id": "a8af240ccd937c26ef72-8427e22c1c1387e051ed", "file": "auth/backend-integration.test.ts", "line": 96, "column": 9}, {"title": "should validate NextAuth configuration", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 2, "status": "passed", "duration": 588, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:39.206Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a8af240ccd937c26ef72-6fd0d5caf2444fdf4b8d", "file": "auth/backend-integration.test.ts", "line": 110, "column": 9}]}, {"title": "Error Handling and Logging", "file": "auth/backend-integration.test.ts", "line": 126, "column": 8, "specs": [{"title": "should return proper error responses", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 3, "status": "failed", "duration": 660, "error": {"message": "SyntaxError: Unexpected non-whitespace character after JSON at position 4 (line 1 column 5)", "stack": "SyntaxError: Unexpected non-whitespace character after JSON at position 4 (line 1 column 5)\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:132:20", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 20, "line": 132}, "snippet": "\u001b[0m \u001b[90m 130 |\u001b[39m       expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m404\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 131 |\u001b[39m       \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 132 |\u001b[39m       \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m response\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 133 |\u001b[39m       expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'error'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 134 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 135 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 20, "line": 132}, "message": "SyntaxError: Unexpected non-whitespace character after JSO<PERSON> at position 4 (line 1 column 5)\n\n\u001b[0m \u001b[90m 130 |\u001b[39m       expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m404\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 131 |\u001b[39m       \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 132 |\u001b[39m       \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m response\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 133 |\u001b[39m       expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'error'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 134 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 135 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:132:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:38.599Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-690ac-turn-proper-error-responses-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-690ac-turn-proper-error-responses-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 20, "line": 132}}], "status": "unexpected"}], "id": "a8af240ccd937c26ef72-795f1a4462724cc5092b", "file": "auth/backend-integration.test.ts", "line": 128, "column": 9}, {"title": "should handle server errors gracefully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 699, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m404\u001b[39m\nReceived array: \u001b[31m[400, 401, 500]\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m404\u001b[39m\nReceived array: \u001b[31m[400, 401, 500]\u001b[39m\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:145:31", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 31, "line": 145}, "snippet": "\u001b[0m \u001b[90m 143 |\u001b[39m       })\u001b[33m;\u001b[39m\n \u001b[90m 144 |\u001b[39m       \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 145 |\u001b[39m       expect([\u001b[35m400\u001b[39m\u001b[33m,\u001b[39m \u001b[35m401\u001b[39m\u001b[33m,\u001b[39m \u001b[35m500\u001b[39m])\u001b[33m.\u001b[39mtoContain(response\u001b[33m.\u001b[39mstatus())\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 146 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 147 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 148 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 31, "line": 145}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m404\u001b[39m\nReceived array: \u001b[31m[400, 401, 500]\u001b[39m\n\n\u001b[0m \u001b[90m 143 |\u001b[39m       })\u001b[33m;\u001b[39m\n \u001b[90m 144 |\u001b[39m       \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 145 |\u001b[39m       expect([\u001b[35m400\u001b[39m\u001b[33m,\u001b[39m \u001b[35m401\u001b[39m\u001b[33m,\u001b[39m \u001b[35m500\u001b[39m])\u001b[33m.\u001b[39mtoContain(response\u001b[33m.\u001b[39mstatus())\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 146 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 147 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 148 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:145:31\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:39.714Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-7218a-le-server-errors-gracefully-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-7218a-le-server-errors-gracefully-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-7218a-le-server-errors-gracefully-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 31, "line": 145}}], "status": "unexpected"}], "id": "a8af240ccd937c26ef72-e9a0a5d1eb14d55b042e", "file": "auth/backend-integration.test.ts", "line": 136, "column": 9}]}, {"title": "Security Measures", "file": "auth/backend-integration.test.ts", "line": 149, "column": 8, "specs": [{"title": "should include security headers", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 1, "status": "passed", "duration": 1119, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:40.806Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a8af240ccd937c26ef72-f659a737b391c659e3f2", "file": "auth/backend-integration.test.ts", "line": 151, "column": 9}, {"title": "should handle rate limiting", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 3, "status": "passed", "duration": 1119, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:41.079Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a8af240ccd937c26ef72-d7c292114ce5ef2b8ada", "file": "auth/backend-integration.test.ts", "line": 161, "column": 9}, {"title": "should validate input sanitization", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 2, "status": "failed", "duration": 508, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m404\u001b[39m\nReceived array: \u001b[31m[400, 401, 422]\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m404\u001b[39m\nReceived array: \u001b[31m[400, 401, 422]\u001b[39m\n    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:191:33", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 33, "line": 191}, "snippet": "\u001b[0m \u001b[90m 189 |\u001b[39m         \n \u001b[90m 190 |\u001b[39m         \u001b[90m// Should reject or sanitize malicious input\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 191 |\u001b[39m         expect([\u001b[35m400\u001b[39m\u001b[33m,\u001b[39m \u001b[35m401\u001b[39m\u001b[33m,\u001b[39m \u001b[35m422\u001b[39m])\u001b[33m.\u001b[39mtoContain(response\u001b[33m.\u001b[39mstatus())\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 192 |\u001b[39m       }\n \u001b[90m 193 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 194 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 33, "line": 191}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m404\u001b[39m\nReceived array: \u001b[31m[400, 401, 422]\u001b[39m\n\n\u001b[0m \u001b[90m 189 |\u001b[39m         \n \u001b[90m 190 |\u001b[39m         \u001b[90m// Should reject or sanitize malicious input\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 191 |\u001b[39m         expect([\u001b[35m400\u001b[39m\u001b[33m,\u001b[39m \u001b[35m401\u001b[39m\u001b[33m,\u001b[39m \u001b[35m422\u001b[39m])\u001b[33m.\u001b[39mtoContain(response\u001b[33m.\u001b[39mstatus())\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 192 |\u001b[39m       }\n \u001b[90m 193 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 194 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts:191:33\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:40.400Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-98927-validate-input-sanitization-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-98927-validate-input-sanitization-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/tests/test-results/auth-backend-integration-B-98927-validate-input-sanitization-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/auth/backend-integration.test.ts", "column": 33, "line": 191}}], "status": "unexpected"}], "id": "a8af240ccd937c26ef72-dd05f2c24ed71f8bfce8", "file": "auth/backend-integration.test.ts", "line": 175, "column": 9}]}, {"title": "Performance and Reliability", "file": "auth/backend-integration.test.ts", "line": 196, "column": 8, "specs": [{"title": "should meet response time requirements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "passed", "duration": 380, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:42.701Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a8af240ccd937c26ef72-c3cb99997081e56fc8ce", "file": "auth/backend-integration.test.ts", "line": 198, "column": 9}, {"title": "should handle concurrent requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "passed", "duration": 399, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-26T04:09:42.681Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a8af240ccd937c26ef72-b1a932ce0a74eb0c3b99", "file": "auth/backend-integration.test.ts", "line": 217, "column": 9}]}]}]}], "errors": [], "stats": {"startTime": "2025-05-26T04:09:27.235Z", "duration": 16382.617, "expected": 7, "skipped": 0, "unexpected": 7, "flaky": 0}}