/**
 * Global Setup for Playwright Tests
 * 
 * This file runs once before all tests to set up the test environment.
 */

import { chromium, FullConfig } from '@playwright/test';
import { TestEnvironment } from './playwright-setup';
import { GLOBAL_TEST_ACCOUNT, TEST_CONFIG } from './test-accounts';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup...');

  // Verify test environment
  try {
    await TestEnvironment.verifyEnvironment();
    console.log('✅ Test environment verified');
  } catch (error) {
    console.error('❌ Test environment verification failed:', error);
    throw error;
  }

  // Verify global test account
  try {
    const response = await fetch(`${TEST_CONFIG.BACKEND_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: GLOBAL_TEST_ACCOUNT.email,
        password: GLOBAL_TEST_ACCOUNT.password,
      }),
    });

    if (!response.ok) {
      throw new Error(`Global test account login failed: ${response.statusText}`);
    }

    console.log('✅ Global test account verified');
  } catch (error) {
    console.error('❌ Global test account verification failed:', error);
    throw error;
  }

  // Create screenshots directory
  const fs = require('fs');
  const screenshotsDir = 'screenshots';
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
    console.log('📁 Created screenshots directory');
  }

  // Create test-results directory
  const testResultsDir = 'test-results';
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
    console.log('📁 Created test-results directory');
  }

  console.log('🎉 Global setup completed successfully');
}

export default globalSetup;
