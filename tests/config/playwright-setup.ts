/**
 * Playwright Test Setup Configuration
 * 
 * This file contains setup utilities for Playwright tests using the global test accounts.
 */

import { test as base, expect } from '@playwright/test';
import { GLOBAL_TEST_ACCOUNT, TEST_CONFIG, loginWithTestAccount, logoutCurrentUser } from './test-accounts';

// Extend the base test with custom fixtures
export const test = base.extend<{
  authenticatedPage: any;
  globalTestUser: any;
}>({
  // Fixture for authenticated page with global test user
  authenticatedPage: async ({ page }, use) => {
    // Login with global test account
    await loginWithTestAccount(GLOBAL_TEST_ACCOUNT, page);
    
    // Use the authenticated page
    await use(page);
    
    // Cleanup: logout after test
    await logoutCurrentUser(page);
  },

  // Fixture for global test user data
  globalTestUser: async ({}, use) => {
    await use(GLOBAL_TEST_ACCOUNT);
  },
});

// Export expect for convenience
export { expect };

/**
 * Custom test utilities
 */
export class TestUtils {
  /**
   * Wait for page to load completely
   */
  static async waitForPageLoad(page: any, timeout = TEST_CONFIG.DEFAULT_TIMEOUT) {
    await page.waitForLoadState('networkidle', { timeout });
  }

  /**
   * Take screenshot with timestamp
   */
  static async takeScreenshot(page: any, name: string) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    await page.screenshot({ 
      path: `tests/screenshots/${name}-${timestamp}.png`,
      fullPage: true 
    });
  }

  /**
   * Fill form and submit
   */
  static async fillAndSubmitForm(page: any, formData: Record<string, string>, submitSelector = 'button[type="submit"]') {
    for (const [field, value] of Object.entries(formData)) {
      await page.fill(`input[name="${field}"]`, value);
    }
    await page.click(submitSelector);
  }

  /**
   * Wait for toast message
   */
  static async waitForToast(page: any, expectedText?: string, timeout = 5000) {
    const toastSelector = '[data-sonner-toast]';
    await page.waitForSelector(toastSelector, { timeout });
    
    if (expectedText) {
      const toastText = await page.textContent(toastSelector);
      expect(toastText).toContain(expectedText);
    }
  }

  /**
   * Check if user is authenticated
   */
  static async isUserAuthenticated(page: any): Promise<boolean> {
    try {
      const response = await page.request.get(`${TEST_CONFIG.FRONTEND_URL}/api/auth/session`);
      const session = await response.json();
      return !!session.user;
    } catch {
      return false;
    }
  }

  /**
   * Navigate and wait for page load
   */
  static async navigateAndWait(page: any, url: string) {
    await page.goto(url);
    await this.waitForPageLoad(page);
  }

  /**
   * Check for error messages on page
   */
  static async checkForErrors(page: any): Promise<string[]> {
    const errorSelectors = [
      '[role="alert"]',
      '.text-red-500',
      '.text-destructive',
      '[data-testid*="error"]'
    ];
    
    const errors: string[] = [];
    
    for (const selector of errorSelectors) {
      try {
        const elements = await page.locator(selector).all();
        for (const element of elements) {
          const text = await element.textContent();
          if (text && text.trim()) {
            errors.push(text.trim());
          }
        }
      } catch {
        // Selector not found, continue
      }
    }
    
    return errors;
  }

  /**
   * Wait for API call to complete
   */
  static async waitForApiCall(page: any, urlPattern: string, timeout = TEST_CONFIG.API_TIMEOUT) {
    return page.waitForResponse(
      (response: any) => response.url().includes(urlPattern) && response.status() < 400,
      { timeout }
    );
  }
}

/**
 * Common test data generators
 */
export class TestDataGenerator {
  /**
   * Generate unique email for testing
   */
  static generateTestEmail(prefix = 'test'): string {
    const timestamp = Date.now();
    return `${prefix}-${timestamp}@${TEST_CONFIG.TEST_DOMAIN}`;
  }

  /**
   * Generate test user data
   */
  static generateTestUser(overrides: Partial<any> = {}) {
    return {
      name: 'Test User',
      email: this.generateTestEmail(),
      password: TEST_CONFIG.DEFAULT_PASSWORD,
      ...overrides
    };
  }

  /**
   * Generate test organization data
   */
  static generateTestOrganization(overrides: Partial<any> = {}) {
    const timestamp = Date.now();
    return {
      name: `Test Organization ${timestamp}`,
      description: 'Test organization for automated testing',
      email: this.generateTestEmail('org'),
      ...overrides
    };
  }
}

/**
 * Test environment checks
 */
export class TestEnvironment {
  /**
   * Check if backend is running
   */
  static async isBackendRunning(): Promise<boolean> {
    try {
      const response = await fetch(`${TEST_CONFIG.BACKEND_URL}/health`);
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Check if frontend is running
   */
  static async isFrontendRunning(): Promise<boolean> {
    try {
      const response = await fetch(TEST_CONFIG.FRONTEND_URL);
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Verify test environment is ready
   */
  static async verifyEnvironment(): Promise<void> {
    const backendRunning = await this.isBackendRunning();
    const frontendRunning = await this.isFrontendRunning();

    if (!backendRunning) {
      throw new Error(`Backend not running at ${TEST_CONFIG.BACKEND_URL}`);
    }

    if (!frontendRunning) {
      throw new Error(`Frontend not running at ${TEST_CONFIG.FRONTEND_URL}`);
    }

    console.log('✅ Test environment verified - Backend and Frontend are running');
  }
}
