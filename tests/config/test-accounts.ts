/**
 * Global Test Accounts Configuration
 * 
 * This file contains test user accounts that can be used across all test files.
 * These accounts are created in the test database and should be used for E2E testing.
 */

export interface TestAccount {
  id: string;
  name: string;
  email: string;
  password: string;
  role: string;
  description: string;
}

/**
 * Primary test account for general testing
 * Created via Go backend API registration
 */
export const GLOBAL_TEST_ACCOUNT: TestAccount = {
  id: "8a68c538-d9f4-49d6-991b-d02138811c5d",
  name: "Test User Global",
  email: "<EMAIL>",
  password: "testpassword123",
  role: "staff",
  description: "Primary test account for E2E testing and general test scenarios"
};

/**
 * Additional test accounts for different scenarios
 */
export const TEST_ACCOUNTS = {
  // Primary test account
  GLOBAL: GLOBAL_TEST_ACCOUNT,
  
  // Admin test account (to be created if needed)
  ADMIN: {
    id: "",
    name: "Test Admin User",
    email: "<EMAIL>",
    password: "adminpassword123",
    role: "admin",
    description: "Admin test account for testing admin-specific functionality"
  } as TestAccount,
  
  // Manager test account (to be created if needed)
  MANAGER: {
    id: "",
    name: "Test Manager User", 
    email: "<EMAIL>",
    password: "managerpassword123",
    role: "manager",
    description: "Manager test account for testing manager-specific functionality"
  } as TestAccount,
  
  // Regular user test account (to be created if needed)
  USER: {
    id: "",
    name: "Test Regular User",
    email: "<EMAIL>", 
    password: "userpassword123",
    role: "user",
    description: "Regular user test account for testing standard user functionality"
  } as TestAccount
};

/**
 * Test environment configuration
 */
export const TEST_CONFIG = {
  // Base URLs
  FRONTEND_URL: process.env.TEST_FRONTEND_URL || "http://localhost:3001",
  BACKEND_URL: process.env.TEST_BACKEND_URL || "http://localhost:8050",
  
  // Test timeouts
  DEFAULT_TIMEOUT: 30000,
  NAVIGATION_TIMEOUT: 10000,
  API_TIMEOUT: 5000,
  
  // Test data
  DEFAULT_PASSWORD: "testpassword123",
  TEST_DOMAIN: "global.test",
  
  // Browser settings for Puppeteer
  BROWSER_OPTIONS: {
    headless: process.env.TEST_HEADLESS !== "false",
    slowMo: process.env.TEST_SLOW_MO ? parseInt(process.env.TEST_SLOW_MO) : 0,
    devtools: process.env.TEST_DEVTOOLS === "true"
  }
};

/**
 * Helper function to get test account by role
 */
export function getTestAccountByRole(role: string): TestAccount | undefined {
  return Object.values(TEST_ACCOUNTS).find(account => account.role === role);
}

/**
 * Helper function to create test account via API
 */
export async function createTestAccount(account: Omit<TestAccount, 'id'>): Promise<TestAccount> {
  const response = await fetch(`${TEST_CONFIG.BACKEND_URL}/api/auth/register`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      name: account.name,
      email: account.email,
      password: account.password,
    }),
  });

  if (!response.ok) {
    throw new Error(`Failed to create test account: ${response.statusText}`);
  }

  const result = await response.json();
  
  return {
    ...account,
    id: result.user.id,
  };
}

/**
 * Helper function to login with test account via NextAuth
 */
export async function loginWithTestAccount(account: TestAccount, page: any): Promise<void> {
  // Navigate to login page
  await page.goto(`${TEST_CONFIG.FRONTEND_URL}/en/login`);
  
  // Fill login form
  await page.fill('input[name="email"]', account.email);
  await page.fill('input[name="password"]', account.password);
  
  // Submit form
  await page.click('button[type="submit"]');
  
  // Wait for redirect to dashboard
  await page.waitForURL('**/dashboard', { timeout: TEST_CONFIG.NAVIGATION_TIMEOUT });
}

/**
 * Helper function to logout current user
 */
export async function logoutCurrentUser(page: any): Promise<void> {
  // Look for logout button/link and click it
  try {
    await page.click('[data-testid="logout-button"]');
  } catch {
    // Fallback: navigate to logout URL
    await page.goto(`${TEST_CONFIG.FRONTEND_URL}/api/auth/signout`);
  }
  
  // Wait for redirect to login page
  await page.waitForURL('**/login', { timeout: TEST_CONFIG.NAVIGATION_TIMEOUT });
}

export default TEST_ACCOUNTS;
