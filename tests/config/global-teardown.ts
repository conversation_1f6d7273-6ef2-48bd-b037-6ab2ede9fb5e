/**
 * Global Teardown for Playwright Tests
 * 
 * This file runs once after all tests to clean up the test environment.
 */

import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown...');

  // Clean up any global resources if needed
  // For now, we don't need to clean up anything specific
  
  console.log('✅ Global teardown completed');
}

export default globalTeardown;
