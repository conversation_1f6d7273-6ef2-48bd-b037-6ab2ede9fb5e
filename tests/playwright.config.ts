import { defineConfig, devices } from '@playwright/test';
import { TEST_CONFIG } from './config/test-accounts';

/**
 * Playwright Configuration for ADC Account Web Tests
 * 
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './e2e',
  
  /* Run tests in files in parallel */
  fullyParallel: true,
  
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['list']
  ],
  
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: TEST_CONFIG.FRONTEND_URL,
    
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    
    /* Take screenshot on failure */
    screenshot: 'only-on-failure',
    
    /* Record video on failure */
    video: 'retain-on-failure',
    
    /* Global timeout for each action */
    actionTimeout: TEST_CONFIG.DEFAULT_TIMEOUT,
    
    /* Global timeout for navigation */
    navigationTimeout: TEST_CONFIG.NAVIGATION_TIMEOUT,
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // Use custom browser options from test config
        launchOptions: {
          headless: TEST_CONFIG.BROWSER_OPTIONS.headless,
          slowMo: TEST_CONFIG.BROWSER_OPTIONS.slowMo,
          devtools: TEST_CONFIG.BROWSER_OPTIONS.devtools,
        }
      },
    },

    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        launchOptions: {
          headless: TEST_CONFIG.BROWSER_OPTIONS.headless,
          slowMo: TEST_CONFIG.BROWSER_OPTIONS.slowMo,
        }
      },
    },

    {
      name: 'webkit',
      use: { 
        ...devices['Desktop Safari'],
        launchOptions: {
          headless: TEST_CONFIG.BROWSER_OPTIONS.headless,
          slowMo: TEST_CONFIG.BROWSER_OPTIONS.slowMo,
        }
      },
    },

    /* Test against mobile viewports. */
    {
      name: 'Mobile Chrome',
      use: { 
        ...devices['Pixel 5'],
        launchOptions: {
          headless: TEST_CONFIG.BROWSER_OPTIONS.headless,
          slowMo: TEST_CONFIG.BROWSER_OPTIONS.slowMo,
        }
      },
    },
    {
      name: 'Mobile Safari',
      use: { 
        ...devices['iPhone 12'],
        launchOptions: {
          headless: TEST_CONFIG.BROWSER_OPTIONS.headless,
          slowMo: TEST_CONFIG.BROWSER_OPTIONS.slowMo,
        }
      },
    },

    /* Test against branded browsers. */
    {
      name: 'Microsoft Edge',
      use: { 
        ...devices['Desktop Edge'], 
        channel: 'msedge',
        launchOptions: {
          headless: TEST_CONFIG.BROWSER_OPTIONS.headless,
          slowMo: TEST_CONFIG.BROWSER_OPTIONS.slowMo,
        }
      },
    },
    {
      name: 'Google Chrome',
      use: { 
        ...devices['Desktop Chrome'], 
        channel: 'chrome',
        launchOptions: {
          headless: TEST_CONFIG.BROWSER_OPTIONS.headless,
          slowMo: TEST_CONFIG.BROWSER_OPTIONS.slowMo,
        }
      },
    },
  ],

  /* Global setup and teardown */
  globalSetup: require.resolve('./config/global-setup.ts'),
  globalTeardown: require.resolve('./config/global-teardown.ts'),

  /* Folder for test artifacts such as screenshots, videos, traces, etc. */
  outputDir: 'test-results/',

  /* Configure test timeout */
  timeout: TEST_CONFIG.DEFAULT_TIMEOUT,

  /* Configure expect timeout */
  expect: {
    timeout: 10000,
  },

  /* Run your local dev server before starting the tests */
  webServer: [
    {
      command: 'cd ../go-backend && go run main.go',
      port: 8050,
      reuseExistingServer: !process.env.CI,
      timeout: 30000,
    },
    {
      command: 'cd .. && npm run dev',
      port: 3001,
      reuseExistingServer: !process.env.CI,
      timeout: 60000,
    }
  ],
});
