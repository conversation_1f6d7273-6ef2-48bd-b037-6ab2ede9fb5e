/**
 * Puppeteer Validation Script
 *
 * This script uses <PERSON><PERSON><PERSON><PERSON> to validate the complete login flow:
 * 1. Navigate to login page
 * 2. Fill login form
 * 3. Submit and wait for dashboard
 * 4. Take screenshots and validate elements
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Test configuration
const CONFIG = {
  FRONTEND_URL: 'http://localhost:3000',
  BACKEND_URL: 'http://localhost:8050',
  TEST_ACCOUNT: {
    email: '<EMAIL>',
    password: 'testpassword123'
  },
  SCREENSHOTS_DIR: 'tests/puppeteer-screenshots'
};

async function validateServers() {
  console.log('🔍 Validating servers...');

  try {
    // Check frontend
    const frontendResponse = await fetch(CONFIG.FRONTEND_URL);
    console.log(`✅ Frontend server (${CONFIG.FRONTEND_URL}): ${frontendResponse.status}`);

    // Check backend
    const backendResponse = await fetch(`${CONFIG.BACKEND_URL}/health`);
    console.log(`✅ Backend server (${CONFIG.BACKEND_URL}/health): ${backendResponse.status}`);

    return true;
  } catch (error) {
    console.error('❌ Server validation failed:', error.message);
    return false;
  }
}

async function createScreenshotsDir() {
  if (!fs.existsSync(CONFIG.SCREENSHOTS_DIR)) {
    fs.mkdirSync(CONFIG.SCREENSHOTS_DIR, { recursive: true });
    console.log(`📁 Created screenshots directory: ${CONFIG.SCREENSHOTS_DIR}`);
  }
}

async function validateLoginFlow() {
  console.log('🚀 Starting Puppeteer login flow validation...');

  // Launch browser
  const browser = await puppeteer.launch({
    headless: false, // Set to true for headless mode
    defaultViewport: { width: 1280, height: 720 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const page = await browser.newPage();

  try {
    // Step 1: Navigate to login page
    console.log('📍 Step 1: Navigating to login page...');
    await page.goto(`${CONFIG.FRONTEND_URL}/en/login`, {
      waitUntil: 'networkidle2',
      timeout: 30000
    });

    // Take screenshot of login page
    await page.screenshot({
      path: path.join(CONFIG.SCREENSHOTS_DIR, '01-login-page.png'),
      fullPage: true
    });
    console.log('📸 Screenshot: 01-login-page.png');

    // Step 2: Check for login form elements
    console.log('📍 Step 2: Checking login form elements...');

    await page.waitForSelector('input[name="email"]', { timeout: 10000 });
    await page.waitForSelector('input[name="password"]', { timeout: 10000 });
    await page.waitForSelector('button[type="submit"]', { timeout: 10000 });

    console.log('✅ Login form elements found');

    // Step 3: Fill login form
    console.log('📍 Step 3: Filling login form...');
    console.log(`Using account: ${CONFIG.TEST_ACCOUNT.email}`);

    await page.type('input[name="email"]', CONFIG.TEST_ACCOUNT.email, { delay: 100 });
    await page.type('input[name="password"]', CONFIG.TEST_ACCOUNT.password, { delay: 100 });

    // Take screenshot of filled form
    await page.screenshot({
      path: path.join(CONFIG.SCREENSHOTS_DIR, '02-form-filled.png'),
      fullPage: true
    });
    console.log('📸 Screenshot: 02-form-filled.png');

    // Step 4: Submit form and handle response
    console.log('📍 Step 4: Submitting login form...');

    // Listen for navigation or error messages
    const navigationPromise = page.waitForNavigation({
      waitUntil: 'networkidle2',
      timeout: 15000
    }).catch(() => null); // Don't fail if no navigation occurs

    await page.click('button[type="submit"]');

    // Wait a bit for any immediate response
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Take screenshot after submission
    await page.screenshot({
      path: path.join(CONFIG.SCREENSHOTS_DIR, '03-after-submit.png'),
      fullPage: true
    });
    console.log('📸 Screenshot: 03-after-submit.png');

    // Check current URL
    const currentUrl = page.url();
    console.log(`Current URL: ${currentUrl}`);

    // Check for error messages
    const errorMessages = await page.$$eval('[role="alert"], .alert, .error, .toast',
      elements => elements.map(el => el.textContent.trim())
    ).catch(() => []);

    if (errorMessages.length > 0) {
      console.log('⚠️ Error messages found:');
      errorMessages.forEach(msg => console.log(`   - ${msg}`));
    }

    // Step 5: Check if we're on dashboard or handle redirect
    if (currentUrl.includes('/dashboard')) {
      console.log('✅ Successfully navigated to dashboard!');
      await validateDashboard(page);
    } else if (currentUrl.includes('/login')) {
      console.log('❌ Still on login page - checking for errors...');
      await handleLoginError(page);
    } else {
      console.log(`ℹ️ Redirected to: ${currentUrl}`);
      await page.screenshot({
        path: path.join(CONFIG.SCREENSHOTS_DIR, '04-redirect-page.png'),
        fullPage: true
      });
    }

  } catch (error) {
    console.error('❌ Error during login flow:', error.message);

    // Take error screenshot
    await page.screenshot({
      path: path.join(CONFIG.SCREENSHOTS_DIR, '99-error.png'),
      fullPage: true
    });
    console.log('📸 Error screenshot: 99-error.png');
  } finally {
    await browser.close();
  }
}

async function validateDashboard(page) {
  console.log('📍 Validating dashboard page...');

  // Wait for dashboard to load
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Take dashboard screenshot
  await page.screenshot({
    path: path.join(CONFIG.SCREENSHOTS_DIR, '05-dashboard.png'),
    fullPage: true
  });
  console.log('📸 Screenshot: 05-dashboard.png');

  // Check for dashboard elements
  const dashboardTitle = await page.$('h1');
  if (dashboardTitle) {
    const titleText = await page.evaluate(el => el.textContent, dashboardTitle);
    console.log(`✅ Dashboard title: ${titleText}`);
  }

  // Count interactive elements
  const buttonCount = await page.$$eval('button', buttons => buttons.length);
  const linkCount = await page.$$eval('a', links => links.length);

  console.log(`ℹ️ Found ${buttonCount} buttons and ${linkCount} links`);

  // Check for navigation
  const navElements = await page.$$('nav, [role="navigation"]');
  console.log(`ℹ️ Found ${navElements.length} navigation elements`);

  console.log('🎉 Dashboard validation completed!');
}

async function handleLoginError(page) {
  console.log('📍 Handling login error...');

  // Look for specific error messages
  const errorSelectors = [
    '[role="alert"]',
    '.alert',
    '.error',
    '.toast',
    '.notification',
    '[class*="error"]',
    '[class*="alert"]'
  ];

  for (const selector of errorSelectors) {
    try {
      const elements = await page.$$(selector);
      for (const element of elements) {
        const text = await page.evaluate(el => el.textContent.trim(), element);
        if (text) {
          console.log(`❌ Error found: ${text}`);
        }
      }
    } catch (e) {
      // Ignore selector errors
    }
  }

  // Check if it's a rate limiting issue
  const pageContent = await page.content();
  if (pageContent.includes('rate limit') || pageContent.includes('too many')) {
    console.log('⚠️ Rate limiting detected - waiting 30 seconds...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    // Try again
    console.log('🔄 Retrying login...');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));

    const newUrl = page.url();
    if (newUrl.includes('/dashboard')) {
      console.log('✅ Retry successful - navigated to dashboard!');
      await validateDashboard(page);
    } else {
      console.log('❌ Retry failed');
    }
  }
}

async function main() {
  console.log('🎯 Puppeteer Login Flow Validation');
  console.log('=====================================');

  // Create screenshots directory
  await createScreenshotsDir();

  // Validate servers
  const serversOk = await validateServers();
  if (!serversOk) {
    console.log('❌ Servers not available - exiting');
    process.exit(1);
  }

  // Run login flow validation
  await validateLoginFlow();

  console.log('=====================================');
  console.log('🏁 Validation completed!');
  console.log(`📁 Screenshots saved to: ${CONFIG.SCREENSHOTS_DIR}`);
}

// Run the validation
main().catch(console.error);
