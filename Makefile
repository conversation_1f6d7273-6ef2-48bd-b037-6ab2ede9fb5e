# ADC Account Web - Makefile
# This Makefile provides commands to run both frontend and backend services

# Default shell
SHELL := /bin/bash

# Colors for output
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
MAGENTA := \033[35m
CYAN := \033[36m
WHITE := \033[37m
RESET := \033[0m

# Project directories
FRONTEND_DIR := .
BACKEND_DIR := go-backend

# Default target
.DEFAULT_GOAL := help

# Help target
.PHONY: help
help: ## Show this help message
	@echo "$(CYAN)ADC Account Web - Available Commands$(RESET)"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(GREEN)%-20s$(RESET) %s\n", $$1, $$2}'
	@echo ""
	@echo "$(YELLOW)Examples:$(RESET)"
	@echo "  make dev          # Run both frontend and backend"
	@echo "  make frontend     # Run only frontend"
	@echo "  make backend      # Run only backend"
	@echo "  make install      # Install all dependencies"

# Installation targets
.PHONY: install install-frontend install-backend
install: install-frontend install-backend ## Install all dependencies (frontend and backend)

install-frontend: ## Install frontend dependencies
	@echo "$(BLUE)Installing frontend dependencies...$(RESET)"
	npm install

install-backend: ## Install backend dependencies
	@echo "$(BLUE)Installing backend dependencies...$(RESET)"
	cd $(BACKEND_DIR) && go run main.go

# Development targets
.PHONY: dev dev-stripe frontend backend
dev: ## Run both frontend and backend in development mode
	@echo "$(GREEN)Starting both frontend and backend...$(RESET)"
	@echo "$(YELLOW)Frontend will be available at: http://localhost:3000$(RESET)"
	@echo "$(YELLOW)Backend will be available at: http://localhost:8080$(RESET)"
	@echo "$(MAGENTA)Press Ctrl+C to stop both services$(RESET)"
	@echo ""
	./node_modules/.bin/concurrently \
		--names "FRONTEND,BACKEND" \
		--prefix-colors "cyan,magenta" \
		"make frontend-only" \
		"make backend-only"

dev-stripe: ## Run frontend with Stripe webhook support
	@echo "$(GREEN)Starting frontend with Stripe webhook...$(RESET)"
	npm run dev:stripe

frontend: frontend-only ## Run only the frontend development server
frontend-only: ## Run only the frontend development server (internal use)
	@echo "$(CYAN)Starting frontend development server...$(RESET)"
	npm run dev

backend: backend-only ## Run only the backend development server
backend-only: ## Run only the backend development server (internal use)
	@echo "$(MAGENTA)Starting backend development server...$(RESET)"
	cd $(BACKEND_DIR) && cargo run

# Build targets
.PHONY: build build-frontend build-backend
build: build-frontend build-backend ## Build both frontend and backend for production

build-frontend: ## Build frontend for production
	@echo "$(BLUE)Building frontend for production...$(RESET)"
	npm run build

build-backend: ## Build backend for production
	@echo "$(BLUE)Building backend for production...$(RESET)"
	cd $(BACKEND_DIR) && cargo build --release

# Test targets
.PHONY: test test-frontend test-backend
test: test-frontend test-backend ## Run all tests

test-frontend: ## Run frontend tests
	@echo "$(BLUE)Running frontend tests...$(RESET)"
	npm run test:run

test-backend: ## Run backend tests
	@echo "$(BLUE)Running backend tests...$(RESET)"
	cd $(BACKEND_DIR) && cargo test

# Lint targets
.PHONY: lint lint-frontend lint-backend
lint: lint-frontend lint-backend ## Run linting for both frontend and backend

lint-frontend: ## Run frontend linting
	@echo "$(BLUE)Running frontend linting...$(RESET)"
	npm run lint

lint-backend: ## Run backend linting
	@echo "$(BLUE)Running backend linting...$(RESET)"
	cd $(BACKEND_DIR) && cargo clippy

# Format targets
.PHONY: format format-frontend format-backend
format: format-frontend format-backend ## Format code for both frontend and backend

format-frontend: ## Format frontend code
	@echo "$(BLUE)Formatting frontend code...$(RESET)"
	npx prettier --write .

format-backend: ## Format backend code
	@echo "$(BLUE)Formatting backend code...$(RESET)"
	cd $(BACKEND_DIR) && cargo fmt

# Database targets
.PHONY: migrate migrate-audit setup-dev-user
migrate: ## Run database migrations
	@echo "$(BLUE)Running database migrations...$(RESET)"
	npm run migrate

migrate-audit: ## Run audit log migrations
	@echo "$(BLUE)Running audit log migrations...$(RESET)"
	npm run migrate:audit

setup-dev-user: ## Create development user
	@echo "$(BLUE)Creating development user...$(RESET)"
	npm run setup:dev-user

# Clean targets
.PHONY: clean clean-frontend clean-backend clean-all
clean: clean-frontend clean-backend ## Clean build artifacts

clean-frontend: ## Clean frontend build artifacts
	@echo "$(YELLOW)Cleaning frontend build artifacts...$(RESET)"
	rm -rf .next
	rm -rf out
	rm -rf node_modules/.cache

clean-backend: ## Clean backend build artifacts
	@echo "$(YELLOW)Cleaning backend build artifacts...$(RESET)"
	cd $(BACKEND_DIR) && cargo clean

clean-all: clean ## Clean all artifacts including node_modules and target
	@echo "$(RED)Cleaning all artifacts...$(RESET)"
	rm -rf node_modules
	cd $(BACKEND_DIR) && rm -rf target

# Docker targets
.PHONY: docker-build docker-run docker-stop
docker-build: ## Build Docker image for backend
	@echo "$(BLUE)Building Docker image for backend...$(RESET)"
	cd $(BACKEND_DIR) && docker build -t adc-account-backend .

docker-run: ## Run backend in Docker container
	@echo "$(GREEN)Running backend in Docker container...$(RESET)"
	cd $(BACKEND_DIR) && docker run -p 8080:8080 --env-file .env adc-account-backend

docker-stop: ## Stop Docker containers
	@echo "$(YELLOW)Stopping Docker containers...$(RESET)"
	docker stop $$(docker ps -q --filter ancestor=adc-account-backend) 2>/dev/null || true

# Health check targets
.PHONY: health health-frontend health-backend
health: health-frontend health-backend ## Check health of both services

health-frontend: ## Check frontend health
	@echo "$(BLUE)Checking frontend health...$(RESET)"
	@curl -s http://localhost:3000 > /dev/null && echo "$(GREEN)Frontend is running$(RESET)" || echo "$(RED)Frontend is not running$(RESET)"

health-backend: ## Check backend health
	@echo "$(BLUE)Checking backend health...$(RESET)"
	@curl -s http://localhost:8080/health > /dev/null && echo "$(GREEN)Backend is running$(RESET)" || echo "$(RED)Backend is not running$(RESET)"

# Utility targets
.PHONY: logs logs-frontend logs-backend
logs-frontend: ## Show frontend logs (if running in background)
	@echo "$(CYAN)Frontend logs:$(RESET)"
	@echo "Run 'make frontend' to see live logs"

logs-backend: ## Show backend logs (if running in background)
	@echo "$(MAGENTA)Backend logs:$(RESET)"
	@echo "Run 'make backend' to see live logs"

# Quick start target
.PHONY: start
start: install dev ## Quick start: install dependencies and run both services
