import { NextRequest, NextResponse } from 'next/server';
import { getAuthHeaders } from '@/lib/auth/session';

export interface ProxyOptions extends Omit<RequestInit, 'headers'> {
  headers?: Record<string, string>;
  timeout?: number;
}

/**
 * Proxy request to Rust backend service
 */
export async function proxyToRustBackend(
  path: string,
  request: NextRequest,
  options?: ProxyOptions
): Promise<Response> {
  const rustBackendUrl = process.env.NEXT_PUBLIC_GO_BACKEND_URL || process.env.NEXT_PUBLIC_RUST_BACKEND_URL || 'http://localhost:8050';
  const url = `${rustBackendUrl}/api${path}`;

  try {
    // Get auth headers from session
    const authHeaders = await getAuthHeaders();

    // Extract query parameters from the original request
    const searchParams = request.nextUrl.searchParams.toString();
    const fullUrl = searchParams ? `${url}?${searchParams}` : url;

    // Prepare headers
    const headers = {
      'Content-Type': 'application/json',
      'X-Request-ID': crypto.randomUUID(),
      ...authHeaders,
      ...options?.headers,
    };

    // Prepare request options
    const fetchOptions: RequestInit = {
      ...options,
      method: options?.method || request.method,
      headers,
    };

    // Add body if present and not GET/HEAD request
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      // If body is provided in options, use it; otherwise read from request
      if (options?.body) {
        fetchOptions.body = options.body;
      } else {
        try {
          const body = await request.text();
          if (body) {
            fetchOptions.body = body;
          }
        } catch (error) {
          console.error('Error reading request body:', error);
        }
      }
    }

    // Add timeout if specified
    const controller = new AbortController();
    const timeoutId = options?.timeout
      ? setTimeout(() => controller.abort(), options.timeout)
      : null;

    fetchOptions.signal = controller.signal;

    // Make the request
    const response = await fetch(fullUrl, fetchOptions);

    // Clear timeout
    if (timeoutId) clearTimeout(timeoutId);

    return response;
  } catch (error) {
    console.error('Proxy error:', error);

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return new Response(
          JSON.stringify({ error: 'Request timeout' }),
          { status: 504, headers: { 'Content-Type': 'application/json' } }
        );
      }

      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 502, headers: { 'Content-Type': 'application/json' } }
      );
    }

    return new Response(
      JSON.stringify({ error: 'Internal proxy error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

/**
 * Helper to create a NextResponse from a fetch Response
 */
export async function createNextResponse(response: Response): Promise<NextResponse> {
  const data = await response.json().catch(() => null);

  return NextResponse.json(
    data || { error: 'Invalid response from backend' },
    {
      status: response.status,
      headers: {
        'X-Backend-Status': response.status.toString(),
      }
    }
  );
}

/**
 * Proxy helper for GET requests
 */
export async function proxyGet(path: string, request: NextRequest) {
  const response = await proxyToRustBackend(path, request, { method: 'GET' });
  return createNextResponse(response);
}

/**
 * Proxy helper for POST requests
 */
export async function proxyPost(path: string, request: NextRequest, body?: any) {
  const response = await proxyToRustBackend(path, request, {
    method: 'POST',
    body: body ? JSON.stringify(body) : undefined,
  });
  return createNextResponse(response);
}

/**
 * Proxy helper for PUT requests
 */
export async function proxyPut(path: string, request: NextRequest, body?: any) {
  const response = await proxyToRustBackend(path, request, {
    method: 'PUT',
    body: body ? JSON.stringify(body) : undefined,
  });
  return createNextResponse(response);
}

/**
 * Proxy helper for DELETE requests
 */
export async function proxyDelete(path: string, request: NextRequest) {
  const response = await proxyToRustBackend(path, request, { method: 'DELETE' });
  return createNextResponse(response);
}
