'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import {
  useGetFinancialSummaryQuery,
  useGetAccountsReceivableSummaryQuery,
  useGetAccountsPayableSummaryQuery,
  useGetTopCustomersQuery,
  useGetTopVendorsQuery,
  useGetRecentTransactionsQuery,
  useGetCashFlowDataQuery,
} from '@/redux/services/dashboardApi';

interface DashboardFilters {
  period: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
}

interface DashboardState {
  filters: DashboardFilters;
  lastRefresh: Date | null;
  autoRefresh: boolean;
  refreshInterval: number; // in milliseconds
}

interface UseDashboardReturn {
  // Data
  financialSummary: any;
  arSummary: any;
  apSummary: any;
  topCustomers: any[];
  topVendors: any[];
  recentTransactions: any[];
  cashFlowData: any;
  
  // Loading states
  isLoadingFinancial: boolean;
  isLoadingAR: boolean;
  isLoadingAP: boolean;
  isLoadingCustomers: boolean;
  isLoadingVendors: boolean;
  isLoadingTransactions: boolean;
  isLoadingCashFlow: boolean;
  isLoading: boolean;
  
  // Error states
  errorFinancial: any;
  errorAR: any;
  errorAP: any;
  errorCustomers: any;
  errorVendors: any;
  errorTransactions: any;
  errorCashFlow: any;
  hasError: boolean;
  
  // Actions
  refreshAllData: () => void;
  updateFilters: (filters: Partial<DashboardFilters>) => void;
  toggleAutoRefresh: () => void;
  setRefreshInterval: (interval: number) => void;
  
  // State
  filters: DashboardFilters;
  lastRefresh: Date | null;
  autoRefresh: boolean;
  refreshInterval: number;
}

const DEFAULT_FILTERS: DashboardFilters = {
  period: '30days',
  limit: 10,
};

const DEFAULT_REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutes

export const useDashboard = (initialFilters?: Partial<DashboardFilters>): UseDashboardReturn => {
  const { handleApiError } = useErrorHandler();
  
  const [state, setState] = useState<DashboardState>({
    filters: { ...DEFAULT_FILTERS, ...initialFilters },
    lastRefresh: null,
    autoRefresh: false,
    refreshInterval: DEFAULT_REFRESH_INTERVAL,
  });

  // Memoize query parameters to prevent unnecessary re-renders
  const queryParams = useMemo(() => ({
    financial: {
      startDate: state.filters.startDate,
      endDate: state.filters.endDate,
    },
    customers: {
      limit: state.filters.limit,
      period: state.filters.period,
    },
    vendors: {
      limit: state.filters.limit,
      period: state.filters.period,
    },
    transactions: {
      limit: state.filters.limit,
    },
    cashFlow: {
      period: state.filters.period,
    },
  }), [state.filters]);

  // API queries with optimized polling
  const {
    data: financialSummary,
    isLoading: isLoadingFinancial,
    error: errorFinancial,
    refetch: refetchFinancial,
  } = useGetFinancialSummaryQuery(queryParams.financial, {
    pollingInterval: state.autoRefresh ? state.refreshInterval : 0,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: arSummary,
    isLoading: isLoadingAR,
    error: errorAR,
    refetch: refetchAR,
  } = useGetAccountsReceivableSummaryQuery({}, {
    pollingInterval: state.autoRefresh ? state.refreshInterval : 0,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: apSummary,
    isLoading: isLoadingAP,
    error: errorAP,
    refetch: refetchAP,
  } = useGetAccountsPayableSummaryQuery({}, {
    pollingInterval: state.autoRefresh ? state.refreshInterval : 0,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: topCustomers,
    isLoading: isLoadingCustomers,
    error: errorCustomers,
    refetch: refetchCustomers,
  } = useGetTopCustomersQuery(queryParams.customers, {
    pollingInterval: state.autoRefresh ? state.refreshInterval : 0,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: topVendors,
    isLoading: isLoadingVendors,
    error: errorVendors,
    refetch: refetchVendors,
  } = useGetTopVendorsQuery(queryParams.vendors, {
    pollingInterval: state.autoRefresh ? state.refreshInterval : 0,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: recentTransactions,
    isLoading: isLoadingTransactions,
    error: errorTransactions,
    refetch: refetchTransactions,
  } = useGetRecentTransactionsQuery(queryParams.transactions, {
    pollingInterval: state.autoRefresh ? state.refreshInterval : 0,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: cashFlowData,
    isLoading: isLoadingCashFlow,
    error: errorCashFlow,
    refetch: refetchCashFlow,
  } = useGetCashFlowDataQuery(queryParams.cashFlow, {
    pollingInterval: state.autoRefresh ? state.refreshInterval : 0,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  // Aggregate loading and error states
  const isLoading = useMemo(() => 
    isLoadingFinancial || isLoadingAR || isLoadingAP || 
    isLoadingCustomers || isLoadingVendors || isLoadingTransactions || isLoadingCashFlow,
    [isLoadingFinancial, isLoadingAR, isLoadingAP, isLoadingCustomers, 
     isLoadingVendors, isLoadingTransactions, isLoadingCashFlow]
  );

  const hasError = useMemo(() => 
    !!(errorFinancial || errorAR || errorAP || errorCustomers || 
       errorVendors || errorTransactions || errorCashFlow),
    [errorFinancial, errorAR, errorAP, errorCustomers, 
     errorVendors, errorTransactions, errorCashFlow]
  );

  // Actions
  const refreshAllData = useCallback(async () => {
    try {
      await Promise.all([
        refetchFinancial(),
        refetchAR(),
        refetchAP(),
        refetchCustomers(),
        refetchVendors(),
        refetchTransactions(),
        refetchCashFlow(),
      ]);
      
      setState(prev => ({
        ...prev,
        lastRefresh: new Date(),
      }));
    } catch (error) {
      handleApiError(error);
    }
  }, [
    refetchFinancial, refetchAR, refetchAP, refetchCustomers,
    refetchVendors, refetchTransactions, refetchCashFlow, handleApiError
  ]);

  const updateFilters = useCallback((newFilters: Partial<DashboardFilters>) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...newFilters },
    }));
  }, []);

  const toggleAutoRefresh = useCallback(() => {
    setState(prev => ({
      ...prev,
      autoRefresh: !prev.autoRefresh,
    }));
  }, []);

  const setRefreshInterval = useCallback((interval: number) => {
    setState(prev => ({
      ...prev,
      refreshInterval: interval,
    }));
  }, []);

  // Handle errors
  useEffect(() => {
    if (hasError) {
      const errors = [
        errorFinancial,
        errorAR,
        errorAP,
        errorCustomers,
        errorVendors,
        errorTransactions,
        errorCashFlow,
      ].filter(Boolean);

      errors.forEach(error => {
        handleApiError(error);
      });
    }
  }, [hasError, errorFinancial, errorAR, errorAP, errorCustomers, 
      errorVendors, errorTransactions, errorCashFlow, handleApiError]);

  // Performance monitoring
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const startTime = performance.now();
      
      return () => {
        const endTime = performance.now();
        console.log(`Dashboard hook execution time: ${endTime - startTime}ms`);
      };
    }
  }, []);

  return {
    // Data
    financialSummary,
    arSummary,
    apSummary,
    topCustomers: topCustomers || [],
    topVendors: topVendors || [],
    recentTransactions: recentTransactions || [],
    cashFlowData,
    
    // Loading states
    isLoadingFinancial,
    isLoadingAR,
    isLoadingAP,
    isLoadingCustomers,
    isLoadingVendors,
    isLoadingTransactions,
    isLoadingCashFlow,
    isLoading,
    
    // Error states
    errorFinancial,
    errorAR,
    errorAP,
    errorCustomers,
    errorVendors,
    errorTransactions,
    errorCashFlow,
    hasError,
    
    // Actions
    refreshAllData,
    updateFilters,
    toggleAutoRefresh,
    setRefreshInterval,
    
    // State
    filters: state.filters,
    lastRefresh: state.lastRefresh,
    autoRefresh: state.autoRefresh,
    refreshInterval: state.refreshInterval,
  };
};
