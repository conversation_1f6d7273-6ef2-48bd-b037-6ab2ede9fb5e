'use client';

import React, { memo, useMemo, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TrendingUp, TrendingDown, BarChart3, LineChart, Download } from 'lucide-react';
import { useI18n } from '@/hooks/useI18n';

interface CashFlowData {
  labels: string[];
  income: number[];
  expenses: number[];
}

interface CashFlowChartProps {
  data: CashFlowData | null;
  isLoading: boolean;
  error: any;
  onPeriodChange?: (period: string) => void;
  onExport?: () => void;
}

type ChartType = 'table' | 'bar' | 'line';

// Chart type selector component
const ChartTypeSelector = memo<{
  chartType: ChartType;
  onTypeChange: (type: ChartType) => void;
}>(({ chartType, onTypeChange }) => {
  const { t } = useI18n('dashboard');

  return (
    <div className="flex space-x-2">
      <Button
        variant={chartType === 'table' ? 'default' : 'outline'}
        size="sm"
        onClick={() => onTypeChange('table')}
        className="flex items-center space-x-1"
      >
        <BarChart3 className="h-4 w-4" />
        <span>{t('table')}</span>
      </Button>
      <Button
        variant={chartType === 'bar' ? 'default' : 'outline'}
        size="sm"
        onClick={() => onTypeChange('bar')}
        className="flex items-center space-x-1"
      >
        <BarChart3 className="h-4 w-4" />
        <span>{t('barChart')}</span>
      </Button>
      <Button
        variant={chartType === 'line' ? 'default' : 'outline'}
        size="sm"
        onClick={() => onTypeChange('line')}
        className="flex items-center space-x-1"
      >
        <LineChart className="h-4 w-4" />
        <span>{t('lineChart')}</span>
      </Button>
    </div>
  );
});

ChartTypeSelector.displayName = 'ChartTypeSelector';

// Cash flow table component
const CashFlowTable = memo<{
  data: CashFlowData;
  formatCurrency: (amount: number) => string;
}>(({ data, formatCurrency }) => {
  const { t } = useI18n('dashboard');

  const tableData = useMemo(() => {
    return data.labels.map((label, index) => ({
      period: label,
      income: data.income[index] || 0,
      expenses: data.expenses[index] || 0,
      netFlow: (data.income[index] || 0) - (data.expenses[index] || 0),
    }));
  }, [data]);

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('period')}</TableHead>
            <TableHead className="text-right">{t('income')}</TableHead>
            <TableHead className="text-right">{t('expenses')}</TableHead>
            <TableHead className="text-right">{t('netCashFlow')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tableData.map((row, index) => (
            <TableRow key={index}>
              <TableCell className="font-medium">{row.period}</TableCell>
              <TableCell className="text-right text-green-600">
                {formatCurrency(row.income)}
              </TableCell>
              <TableCell className="text-right text-red-600">
                {formatCurrency(row.expenses)}
              </TableCell>
              <TableCell className={`text-right font-medium ${
                row.netFlow >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                <div className="flex items-center justify-end space-x-1">
                  {row.netFlow >= 0 ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <TrendingDown className="h-4 w-4" />
                  )}
                  <span>{formatCurrency(Math.abs(row.netFlow))}</span>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
});

CashFlowTable.displayName = 'CashFlowTable';

// Simple bar chart component (placeholder for actual chart library)
const SimpleBarChart = memo<{
  data: CashFlowData;
  formatCurrency: (amount: number) => string;
}>(({ data, formatCurrency }) => {
  const { t } = useI18n('dashboard');

  const maxValue = useMemo(() => {
    const allValues = [...data.income, ...data.expenses];
    return Math.max(...allValues);
  }, [data]);

  return (
    <div className="space-y-4">
      <div className="flex justify-center space-x-6 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-green-500 rounded"></div>
          <span>{t('income')}</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-red-500 rounded"></div>
          <span>{t('expenses')}</span>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {data.labels.map((label, index) => {
          const income = data.income[index] || 0;
          const expenses = data.expenses[index] || 0;
          const incomeHeight = (income / maxValue) * 100;
          const expensesHeight = (expenses / maxValue) * 100;

          return (
            <div key={index} className="text-center">
              <div className="text-xs font-medium mb-2">{label}</div>
              <div className="flex justify-center space-x-2 h-32 items-end">
                <div className="flex flex-col items-center">
                  <div
                    className="w-8 bg-green-500 rounded-t"
                    style={{ height: `${incomeHeight}%` }}
                    title={`Income: ${formatCurrency(income)}`}
                  ></div>
                  <div className="text-xs mt-1 text-green-600">
                    {formatCurrency(income)}
                  </div>
                </div>
                <div className="flex flex-col items-center">
                  <div
                    className="w-8 bg-red-500 rounded-t"
                    style={{ height: `${expensesHeight}%` }}
                    title={`Expenses: ${formatCurrency(expenses)}`}
                  ></div>
                  <div className="text-xs mt-1 text-red-600">
                    {formatCurrency(expenses)}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
});

SimpleBarChart.displayName = 'SimpleBarChart';

// Cash flow summary component
const CashFlowSummary = memo<{
  data: CashFlowData;
  formatCurrency: (amount: number) => string;
}>(({ data, formatCurrency }) => {
  const { t } = useI18n('dashboard');

  const summary = useMemo(() => {
    const totalIncome = data.income.reduce((sum, val) => sum + val, 0);
    const totalExpenses = data.expenses.reduce((sum, val) => sum + val, 0);
    const netCashFlow = totalIncome - totalExpenses;
    const avgIncome = totalIncome / data.income.length;
    const avgExpenses = totalExpenses / data.expenses.length;

    return {
      totalIncome,
      totalExpenses,
      netCashFlow,
      avgIncome,
      avgExpenses,
    };
  }, [data]);

  return (
    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 p-4 bg-muted/50 rounded-lg">
      <div className="text-center">
        <div className="text-sm text-muted-foreground">{t('totalIncome')}</div>
        <div className="text-lg font-semibold text-green-600">
          {formatCurrency(summary.totalIncome)}
        </div>
      </div>
      <div className="text-center">
        <div className="text-sm text-muted-foreground">{t('totalExpenses')}</div>
        <div className="text-lg font-semibold text-red-600">
          {formatCurrency(summary.totalExpenses)}
        </div>
      </div>
      <div className="text-center">
        <div className="text-sm text-muted-foreground">{t('netCashFlow')}</div>
        <div className={`text-lg font-semibold ${
          summary.netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'
        }`}>
          {formatCurrency(summary.netCashFlow)}
        </div>
      </div>
      <div className="text-center">
        <div className="text-sm text-muted-foreground">{t('avgIncome')}</div>
        <div className="text-lg font-semibold text-green-600">
          {formatCurrency(summary.avgIncome)}
        </div>
      </div>
      <div className="text-center">
        <div className="text-sm text-muted-foreground">{t('avgExpenses')}</div>
        <div className="text-lg font-semibold text-red-600">
          {formatCurrency(summary.avgExpenses)}
        </div>
      </div>
    </div>
  );
});

CashFlowSummary.displayName = 'CashFlowSummary';

// Main cash flow chart component
export const CashFlowChart = memo<CashFlowChartProps>(({
  data,
  isLoading,
  error,
  onPeriodChange,
  onExport,
}) => {
  const { t, formatCurrency } = useI18n('dashboard');
  const [chartType, setChartType] = useState<ChartType>('table');
  const [selectedPeriod, setSelectedPeriod] = useState('6months');

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
    onPeriodChange?.(period);
  };

  const renderChart = () => {
    if (!data || !data.labels || data.labels.length === 0) {
      return (
        <div className="h-[300px] flex items-center justify-center">
          <p className="text-muted-foreground">{t('noCashFlowData')}</p>
        </div>
      );
    }

    switch (chartType) {
      case 'bar':
        return <SimpleBarChart data={data} formatCurrency={formatCurrency} />;
      case 'line':
        return (
          <div className="h-[300px] flex items-center justify-center">
            <p className="text-muted-foreground">{t('lineChartComingSoon')}</p>
          </div>
        );
      case 'table':
      default:
        return <CashFlowTable data={data} formatCurrency={formatCurrency} />;
    }
  };

  if (isLoading) {
    return (
      <Card className="mb-8">
        <CardHeader>
          <CardTitle><Skeleton className="h-6 w-32" /></CardTitle>
          <CardDescription><Skeleton className="h-4 w-48" /></CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-[300px] w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="mb-8 border-red-200">
        <CardHeader>
          <CardTitle className="text-red-600">{t('cashFlow')}</CardTitle>
          <CardDescription className="text-red-500">
            {t('errorLoadingCashFlow')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            {t('cashFlowErrorMessage')}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-8">
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div>
            <CardTitle>{t('cashFlow')}</CardTitle>
            <CardDescription>{t('incomeVsExpenses')}</CardDescription>
          </div>
          <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
            <Select value={selectedPeriod} onValueChange={handlePeriodChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t('selectPeriod')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="3months">{t('last3Months')}</SelectItem>
                <SelectItem value="6months">{t('last6Months')}</SelectItem>
                <SelectItem value="12months">{t('last12Months')}</SelectItem>
                <SelectItem value="24months">{t('last24Months')}</SelectItem>
              </SelectContent>
            </Select>
            <ChartTypeSelector chartType={chartType} onTypeChange={setChartType} />
            {onExport && (
              <Button variant="outline" size="sm" onClick={onExport}>
                <Download className="h-4 w-4 mr-2" />
                {t('export')}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {data && <CashFlowSummary data={data} formatCurrency={formatCurrency} />}
        <div className="min-h-[300px]">
          {renderChart()}
        </div>
      </CardContent>
    </Card>
  );
});

CashFlowChart.displayName = 'CashFlowChart';
