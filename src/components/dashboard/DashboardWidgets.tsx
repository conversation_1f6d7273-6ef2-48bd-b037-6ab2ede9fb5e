'use client';

import React, { memo, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { TrendingUp, TrendingDown, DollarSign, CreditCard, Users, ShoppingBag } from 'lucide-react';
import { useI18n } from '@/hooks/useI18n';

interface FinancialSummary {
  total_revenue: number;
  total_expenses: number;
  net_income: number;
  accounts_receivable: number;
  accounts_payable: number;
  cash_balance: number;
}

interface FinancialWidgetProps {
  data: FinancialSummary | null;
  isLoading: boolean;
  error: any;
}

interface MetricCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  isLoading: boolean;
  formatAsCurrency?: boolean;
}

// Memoized metric card component for better performance
const MetricCard = memo<MetricCardProps>(({ 
  title, 
  value, 
  icon, 
  trend, 
  isLoading, 
  formatAsCurrency = true 
}) => {
  const { formatCurrency } = useI18n('dashboard');

  const formattedValue = useMemo(() => {
    if (formatAsCurrency) {
      return formatCurrency(value);
    }
    return value?.toLocaleString() || '0';
  }, [value, formatAsCurrency, formatCurrency]);

  const trendIcon = useMemo(() => {
    if (!trend) return null;
    return trend.isPositive ? (
      <TrendingUp className="h-4 w-4 text-green-500" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-500" />
    );
  }, [trend]);

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <Skeleton className="h-4 w-24" />
          </CardTitle>
          <Skeleton className="h-4 w-4" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-32 mb-2" />
          <Skeleton className="h-3 w-20" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-all duration-200 hover:shadow-md">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formattedValue}</div>
        {trend && (
          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
            {trendIcon}
            <span className={trend.isPositive ? 'text-green-500' : 'text-red-500'}>
              {Math.abs(trend.value)}%
            </span>
            <span>from last period</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

MetricCard.displayName = 'MetricCard';

// Main financial widgets component
export const FinancialWidgets = memo<FinancialWidgetProps>(({ data, isLoading, error }) => {
  const { t } = useI18n('dashboard');

  // Memoize widget configurations to prevent unnecessary re-renders
  const widgets = useMemo(() => [
    {
      key: 'revenue',
      title: t('totalRevenue'),
      value: data?.total_revenue || 0,
      icon: <DollarSign className="h-4 w-4 text-muted-foreground" />,
      trend: data ? {
        value: 12.5, // This would come from API in real implementation
        isPositive: true
      } : undefined,
    },
    {
      key: 'expenses',
      title: t('totalExpenses'),
      value: data?.total_expenses || 0,
      icon: <CreditCard className="h-4 w-4 text-muted-foreground" />,
      trend: data ? {
        value: 8.2,
        isPositive: false
      } : undefined,
    },
    {
      key: 'netIncome',
      title: t('netIncome'),
      value: data?.net_income || 0,
      icon: <TrendingUp className="h-4 w-4 text-muted-foreground" />,
      trend: data ? {
        value: 15.3,
        isPositive: (data.net_income || 0) > 0
      } : undefined,
    },
    {
      key: 'accountsReceivable',
      title: t('accountsReceivable'),
      value: data?.accounts_receivable || 0,
      icon: <Users className="h-4 w-4 text-muted-foreground" />,
    },
    {
      key: 'accountsPayable',
      title: t('accountsPayable'),
      value: data?.accounts_payable || 0,
      icon: <ShoppingBag className="h-4 w-4 text-muted-foreground" />,
    },
    {
      key: 'cashBalance',
      title: t('cashBalance'),
      value: data?.cash_balance || 0,
      icon: <DollarSign className="h-4 w-4 text-muted-foreground" />,
      trend: data ? {
        value: 5.7,
        isPositive: true
      } : undefined,
    },
  ], [data, t]);

  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {Array.from({ length: 6 }).map((_, index) => (
          <Card key={index} className="border-red-200">
            <CardHeader>
              <CardTitle className="text-sm text-red-600">Error Loading Data</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Unable to load financial data. Please try refreshing.
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      {widgets.map((widget) => (
        <MetricCard
          key={widget.key}
          title={widget.title}
          value={widget.value}
          icon={widget.icon}
          trend={widget.trend}
          isLoading={isLoading}
        />
      ))}
    </div>
  );
});

FinancialWidgets.displayName = 'FinancialWidgets';

// Quick stats component for additional metrics
interface QuickStatsProps {
  className?: string;
}

export const QuickStats = memo<QuickStatsProps>(({ className = '' }) => {
  const { t, formatCurrency } = useI18n('dashboard');

  const stats = useMemo(() => [
    {
      label: t('todaysRevenue'),
      value: formatCurrency(8500),
      change: '+12%',
      isPositive: true,
    },
    {
      label: t('pendingInvoices'),
      value: '23',
      change: '-5%',
      isPositive: false,
    },
    {
      label: t('overduePayments'),
      value: formatCurrency(12000),
      change: '+8%',
      isPositive: false,
    },
    {
      label: t('newCustomers'),
      value: '7',
      change: '+15%',
      isPositive: true,
    },
  ], [t, formatCurrency]);

  return (
    <div className={`grid grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {stats.map((stat, index) => (
        <Card key={index} className="p-4">
          <div className="text-sm text-muted-foreground">{stat.label}</div>
          <div className="text-xl font-semibold">{stat.value}</div>
          <div className={`text-xs ${stat.isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {stat.change}
          </div>
        </Card>
      ))}
    </div>
  );
});

QuickStats.displayName = 'QuickStats';

// Performance monitoring component
export const PerformanceIndicator = memo(() => {
  const { t } = useI18n('dashboard');
  
  const [loadTime, setLoadTime] = React.useState<number | null>(null);

  React.useEffect(() => {
    const startTime = performance.now();
    
    // Simulate component load completion
    const timer = setTimeout(() => {
      const endTime = performance.now();
      setLoadTime(endTime - startTime);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white text-xs p-2 rounded">
      {loadTime ? `Load time: ${loadTime.toFixed(2)}ms` : 'Loading...'}
    </div>
  );
});

PerformanceIndicator.displayName = 'PerformanceIndicator';
