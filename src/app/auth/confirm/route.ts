import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const token_hash = searchParams.get('token_hash');
  const type = searchParams.get('type');
  const next = searchParams.get('next') ?? '/';

  console.log('Auth confirm route called with:', { token_hash: !!token_hash, type, next });

  if (token_hash && type) {
    const supabase = createClient();

    try {
      // Verify the token hash
      const { error } = await supabase.auth.verifyOtp({
        token_hash,
        type: type as any,
      });

      if (error) {
        console.error('Token verification failed:', error);
        
        // Redirect to error page with message
        return NextResponse.redirect(
          new URL(`/auth/error?error=${encodeURIComponent(error.message)}`, request.url)
        );
      }

      console.log('Token verified successfully');

      // Handle different types of confirmations
      if (type === 'recovery') {
        // Password reset - redirect to reset password page
        console.log('Password recovery confirmed, redirecting to reset password page');
        return NextResponse.redirect(new URL('/en/reset-password', request.url));
      } else if (type === 'signup') {
        // Email confirmation - redirect to dashboard or login
        console.log('Email signup confirmed, redirecting to dashboard');
        return NextResponse.redirect(new URL('/en/dashboard', request.url));
      } else if (type === 'email_change') {
        // Email change confirmation
        console.log('Email change confirmed, redirecting to profile');
        return NextResponse.redirect(new URL('/en/profile', request.url));
      }

      // Default redirect
      return NextResponse.redirect(new URL(next, request.url));

    } catch (error) {
      console.error('Unexpected error during token verification:', error);
      
      return NextResponse.redirect(
        new URL(`/auth/error?error=${encodeURIComponent('An unexpected error occurred')}`, request.url)
      );
    }
  }

  // No token or type provided - redirect to login
  console.log('No token or type provided, redirecting to login');
  return NextResponse.redirect(new URL('/en/login?message=Invalid or missing confirmation link', request.url));
}
