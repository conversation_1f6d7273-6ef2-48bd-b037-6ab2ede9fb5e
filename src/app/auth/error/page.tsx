'use client';

import React from 'react';
import { useSearchParams } from 'next/navigation';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON><PERSON>riangle, ArrowLeft, RefreshCw } from "lucide-react";
import { Link } from '@/i18n/navigation';

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');

  const getErrorMessage = (error: string | null) => {
    if (!error) return 'An unknown authentication error occurred.';
    
    // Common error messages
    const errorMessages: { [key: string]: string } = {
      'invalid_token': 'The confirmation link is invalid or has expired.',
      'token_expired': 'The confirmation link has expired. Please request a new one.',
      'email_not_confirmed': 'Please check your email and click the confirmation link.',
      'signup_disabled': 'New user registration is currently disabled.',
      'email_address_invalid': 'The email address provided is invalid.',
      'password_too_short': 'Password must be at least 6 characters long.',
      'weak_password': 'Password is too weak. Please choose a stronger password.',
      'email_address_not_authorized': 'This email address is not authorized to sign up.',
      'too_many_requests': 'Too many requests. Please wait before trying again.',
    };

    return errorMessages[error] || error;
  };

  const getErrorTitle = (error: string | null) => {
    if (!error) return 'Authentication Error';
    
    if (error.includes('token') || error.includes('expired')) {
      return 'Link Expired';
    }
    if (error.includes('email')) {
      return 'Email Error';
    }
    if (error.includes('password')) {
      return 'Password Error';
    }
    
    return 'Authentication Error';
  };

  const getSuggestions = (error: string | null) => {
    if (!error) return ['Try signing in again', 'Contact support if the problem persists'];
    
    if (error.includes('token') || error.includes('expired')) {
      return [
        'Request a new password reset link',
        'Check that you clicked the most recent link',
        'Make sure you\'re using the complete link from your email'
      ];
    }
    if (error.includes('email')) {
      return [
        'Check your email address for typos',
        'Make sure you have access to this email account',
        'Check your spam/junk folder'
      ];
    }
    if (error.includes('password')) {
      return [
        'Use at least 8 characters',
        'Include uppercase and lowercase letters',
        'Add numbers and special characters'
      ];
    }
    
    return ['Try the action again', 'Contact support if the problem persists'];
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-2xl text-red-900">
            {getErrorTitle(error)}
          </CardTitle>
          <CardDescription>
            We encountered an issue with your authentication request
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error Details</AlertTitle>
            <AlertDescription>
              {getErrorMessage(error)}
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">What you can try:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              {getSuggestions(error).map((suggestion, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="flex flex-col space-y-2">
            {error?.includes('token') || error?.includes('expired') ? (
              <Button asChild className="w-full">
                <Link href="/forgot-password">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Request New Reset Link
                </Link>
              </Button>
            ) : (
              <Button asChild className="w-full">
                <Link href="/login">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Sign In
                </Link>
              </Button>
            )}
            
            <Button variant="outline" asChild className="w-full">
              <Link href="/">
                Go to Homepage
              </Link>
            </Button>
          </div>

          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              Need help? Contact our support team for assistance.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
