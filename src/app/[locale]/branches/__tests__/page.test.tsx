import { describe, it, expect } from 'vitest'

// Simple Branch Dashboard Testing for ADC-85
// This test focuses on core branch dashboard functionality without complex mocking

describe('BranchesPage - Branch Dashboard Testing (ADC-85)', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset mock context to default state
    mockBranchContext.selectedOrganizationId = 'org-1'
    mockBranchContext.selectedBranchId = null
  })

  describe('Page Rendering and Layout', () => {
    it('renders the main page container with correct structure', () => {
      render(<BranchesPage />)

      const container = screen.getByRole('main') || screen.getByText('Branches').closest('.container')
      expect(container).toBeInTheDocument()
    })

    it('displays the page title and description', () => {
      render(<BranchesPage />)

      expect(screen.getByText('Branches')).toBeInTheDocument()
      expect(screen.getByText('Manage branches for your organizations')).toBeInTheDocument()
    })

    it('renders the Add Branch button', () => {
      render(<BranchesPage />)

      const addButton = screen.getByRole('button', { name: /add branch/i })
      expect(addButton).toBeInTheDocument()
      expect(addButton).not.toBeDisabled()
    })

    it('renders the organization selector card', () => {
      render(<BranchesPage />)

      expect(screen.getByText('Select Organization')).toBeInTheDocument()
      expect(screen.getByText('Choose an organization to view and manage its branches')).toBeInTheDocument()
    })
  })

  describe('Organization Selection', () => {
    it('displays available organizations in the selector', () => {
      render(<BranchesPage />)

      expect(screen.getByText('Test Organization')).toBeInTheDocument()
    })

    it('shows organization details when selected', () => {
      render(<BranchesPage />)

      expect(screen.getByText('Test Organization')).toBeInTheDocument()
      expect(screen.getByText('Test organization description')).toBeInTheDocument()
    })

    it('disables Add Branch button when no organization is selected', () => {
      mockBranchContext.selectedOrganizationId = null
      render(<BranchesPage />)

      const addButton = screen.getByRole('button', { name: /add branch/i })
      expect(addButton).toBeDisabled()
    })
  })

  describe('Branch Data Display', () => {
    it('displays branch cards with correct information', () => {
      render(<BranchesPage />)

      // Check for branch names
      expect(screen.getByText('Downtown Branch')).toBeInTheDocument()
      expect(screen.getByText('Uptown Branch')).toBeInTheDocument()

      // Check for branch descriptions
      expect(screen.getByText('Main downtown location')).toBeInTheDocument()
      expect(screen.getByText('Uptown location')).toBeInTheDocument()

      // Check for manager names
      expect(screen.getByText('Manager: John Manager')).toBeInTheDocument()
      expect(screen.getByText('Manager: Sarah Director')).toBeInTheDocument()

      // Check for branch codes
      expect(screen.getByText('Code: DT001')).toBeInTheDocument()
      expect(screen.getByText('Code: UT001')).toBeInTheDocument()
    })

    it('displays branch contact information', () => {
      render(<BranchesPage />)

      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      expect(screen.getByText('+1234567890')).toBeInTheDocument()
      expect(screen.getByText('+1234567891')).toBeInTheDocument()
    })

    it('displays branch addresses', () => {
      render(<BranchesPage />)

      expect(screen.getByText('123 Main St, City Center')).toBeInTheDocument()
      expect(screen.getByText('456 Oak Ave, Uptown')).toBeInTheDocument()
    })

    it('shows branch status badges', () => {
      render(<BranchesPage />)

      const activeStatuses = screen.getAllByText('Active')
      expect(activeStatuses).toHaveLength(2)
    })

    it('displays creation dates', () => {
      render(<BranchesPage />)

      const createdTexts = screen.getAllByText(/Created 2 days ago/)
      expect(createdTexts).toHaveLength(2)
    })
  })

  describe('Branch Actions', () => {
    it('renders action dropdown menus for each branch', () => {
      render(<BranchesPage />)

      const editButtons = screen.getAllByText('Edit')
      const deleteButtons = screen.getAllByText('Delete')

      expect(editButtons).toHaveLength(2)
      expect(deleteButtons).toHaveLength(2)
    })

    it('opens edit dialog when edit button is clicked', async () => {
      const user = userEvent.setup()
      render(<BranchesPage />)

      const editButtons = screen.getAllByText('Edit')
      await user.click(editButtons[0])

      await waitFor(() => {
        expect(screen.getByTestId('dialog')).toBeInTheDocument()
        expect(screen.getByText('Edit Branch')).toBeInTheDocument()
      })
    })

    it('opens delete dialog when delete button is clicked', async () => {
      const user = userEvent.setup()
      render(<BranchesPage />)

      const deleteButtons = screen.getAllByText('Delete')
      await user.click(deleteButtons[0])

      await waitFor(() => {
        expect(screen.getByTestId('dialog')).toBeInTheDocument()
        expect(screen.getByText('Delete Branch')).toBeInTheDocument()
      })
    })
  })

  describe('Loading States', () => {
    it('displays loading state when fetching branches', () => {
      const mockQuery = vi.mocked(require('@/redux/services/organizationsApi').useGetOrganizationBranchesQuery)
      mockQuery.mockReturnValue({
        data: null,
        isLoading: true,
        isFetching: true,
        error: null,
      })

      render(<BranchesPage />)

      expect(screen.getByText('Loading branches...')).toBeInTheDocument()
    })
  })

  describe('Empty States', () => {
    it('displays empty state when no branches exist', () => {
      const mockQuery = vi.mocked(require('@/redux/services/organizationsApi').useGetOrganizationBranchesQuery)
      mockQuery.mockReturnValue({
        data: { branches: [] },
        isLoading: false,
        isFetching: false,
        error: null,
      })

      render(<BranchesPage />)

      expect(screen.getByText('No branches found')).toBeInTheDocument()
      expect(screen.getByText('Get started by creating your first branch for this organization')).toBeInTheDocument()
    })

    it('displays organization selection prompt when no organization is selected', () => {
      mockBranchContext.selectedOrganizationId = null
      render(<BranchesPage />)

      expect(screen.getByText('Select an Organization')).toBeInTheDocument()
      expect(screen.getByText('Please select an organization above to view and manage its branches')).toBeInTheDocument()
    })
  })

  describe('Create Branch Dialog', () => {
    it('opens create branch dialog when Add Branch button is clicked', async () => {
      const user = userEvent.setup()
      render(<BranchesPage />)

      const addButton = screen.getByRole('button', { name: /add branch/i })
      await user.click(addButton)

      await waitFor(() => {
        expect(screen.getByTestId('dialog')).toBeInTheDocument()
        expect(screen.getByText('Create New Branch')).toBeInTheDocument()
      })
    })

    it('displays all required form fields in create dialog', async () => {
      const user = userEvent.setup()
      render(<BranchesPage />)

      const addButton = screen.getByRole('button', { name: /add branch/i })
      await user.click(addButton)

      await waitFor(() => {
        expect(screen.getByLabelText(/branch name/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/description/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/address/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/phone/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/manager name/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/branch code/i)).toBeInTheDocument()
      })
    })

    it('validates required fields before submission', async () => {
      const user = userEvent.setup()
      render(<BranchesPage />)

      const addButton = screen.getByRole('button', { name: /add branch/i })
      await user.click(addButton)

      await waitFor(() => {
        const submitButton = screen.getByRole('button', { name: /create branch/i })
        expect(submitButton).toBeInTheDocument()
      })

      const submitButton = screen.getByRole('button', { name: /create branch/i })
      await user.click(submitButton)

      // Should show validation errors for required fields
      await waitFor(() => {
        expect(screen.getByText(/branch name is required/i)).toBeInTheDocument()
      })
    })

    it('submits form with valid data', async () => {
      const user = userEvent.setup()
      const mockCreateBranch = vi.fn().mockResolvedValue({
        unwrap: () => Promise.resolve(mockBranchesData.branches[0])
      })

      const mockMutation = vi.mocked(require('@/redux/services/organizationsApi').useCreateBranchMutation)
      mockMutation.mockReturnValue([mockCreateBranch, { isLoading: false }])

      render(<BranchesPage />)

      const addButton = screen.getByRole('button', { name: /add branch/i })
      await user.click(addButton)

      await waitFor(() => {
        expect(screen.getByTestId('dialog')).toBeInTheDocument()
      })

      // Fill out the form
      await user.type(screen.getByLabelText(/branch name/i), 'New Test Branch')
      await user.type(screen.getByLabelText(/description/i), 'Test branch description')
      await user.type(screen.getByLabelText(/address/i), '789 Test St')
      await user.type(screen.getByLabelText(/phone/i), '+1234567892')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/manager name/i), 'Test Manager')
      await user.type(screen.getByLabelText(/branch code/i), 'TEST001')

      const submitButton = screen.getByRole('button', { name: /create branch/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockCreateBranch).toHaveBeenCalledWith({
          organizationId: 'org-1',
          data: {
            name: 'New Test Branch',
            description: 'Test branch description',
            address: '789 Test St',
            phone: '+1234567892',
            email: '<EMAIL>',
            managerName: 'Test Manager',
            branchCode: 'TEST001',
            isActive: true,
          }
        })
      })
    })
  })

  describe('Edit Branch Dialog', () => {
    it('pre-fills form with existing branch data', async () => {
      const user = userEvent.setup()
      render(<BranchesPage />)

      const editButtons = screen.getAllByText('Edit')
      await user.click(editButtons[0])

      await waitFor(() => {
        expect(screen.getByDisplayValue('Downtown Branch')).toBeInTheDocument()
        expect(screen.getByDisplayValue('Main downtown location')).toBeInTheDocument()
        expect(screen.getByDisplayValue('123 Main St, City Center')).toBeInTheDocument()
        expect(screen.getByDisplayValue('+1234567890')).toBeInTheDocument()
        expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
        expect(screen.getByDisplayValue('John Manager')).toBeInTheDocument()
        expect(screen.getByDisplayValue('DT001')).toBeInTheDocument()
      })
    })

    it('updates branch with modified data', async () => {
      const user = userEvent.setup()
      const mockUpdateBranch = vi.fn().mockResolvedValue({
        unwrap: () => Promise.resolve(mockBranchesData.branches[0])
      })

      const mockMutation = vi.mocked(require('@/redux/services/organizationsApi').useUpdateBranchMutation)
      mockMutation.mockReturnValue([mockUpdateBranch, { isLoading: false }])

      render(<BranchesPage />)

      const editButtons = screen.getAllByText('Edit')
      await user.click(editButtons[0])

      await waitFor(() => {
        expect(screen.getByDisplayValue('Downtown Branch')).toBeInTheDocument()
      })

      // Modify the branch name
      const nameInput = screen.getByDisplayValue('Downtown Branch')
      await user.clear(nameInput)
      await user.type(nameInput, 'Updated Downtown Branch')

      const submitButton = screen.getByRole('button', { name: /update branch/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockUpdateBranch).toHaveBeenCalledWith({
          organizationId: 'org-1',
          branchId: 'branch-1',
          data: expect.objectContaining({
            name: 'Updated Downtown Branch',
          })
        })
      })
    })
  })

  describe('Delete Branch Dialog', () => {
    it('shows confirmation dialog with branch details', async () => {
      const user = userEvent.setup()
      render(<BranchesPage />)

      const deleteButtons = screen.getAllByText('Delete')
      await user.click(deleteButtons[0])

      await waitFor(() => {
        expect(screen.getByText('Delete Branch')).toBeInTheDocument()
        expect(screen.getByText(/are you sure you want to delete/i)).toBeInTheDocument()
        expect(screen.getByText('Downtown Branch')).toBeInTheDocument()
      })
    })

    it('deletes branch when confirmed', async () => {
      const user = userEvent.setup()
      const mockDeleteBranch = vi.fn().mockResolvedValue({
        unwrap: () => Promise.resolve({ message: 'Branch deleted' })
      })

      const mockMutation = vi.mocked(require('@/redux/services/organizationsApi').useDeleteBranchMutation)
      mockMutation.mockReturnValue([mockDeleteBranch, { isLoading: false }])

      render(<BranchesPage />)

      const deleteButtons = screen.getAllByText('Delete')
      await user.click(deleteButtons[0])

      await waitFor(() => {
        expect(screen.getByText('Delete Branch')).toBeInTheDocument()
      })

      const confirmButton = screen.getByRole('button', { name: /delete/i })
      await user.click(confirmButton)

      await waitFor(() => {
        expect(mockDeleteBranch).toHaveBeenCalledWith({
          organizationId: 'org-1',
          branchId: 'branch-1',
        })
      })
    })

    it('cancels deletion when cancel button is clicked', async () => {
      const user = userEvent.setup()
      render(<BranchesPage />)

      const deleteButtons = screen.getAllByText('Delete')
      await user.click(deleteButtons[0])

      await waitFor(() => {
        expect(screen.getByText('Delete Branch')).toBeInTheDocument()
      })

      const cancelButton = screen.getByRole('button', { name: /cancel/i })
      await user.click(cancelButton)

      await waitFor(() => {
        expect(screen.queryByText('Delete Branch')).not.toBeInTheDocument()
      })
    })
  })

  describe('Error Handling', () => {
    it('displays error message when API call fails', () => {
      const mockQuery = vi.mocked(require('@/redux/services/organizationsApi').useGetOrganizationBranchesQuery)
      mockQuery.mockReturnValue({
        data: null,
        isLoading: false,
        isFetching: false,
        error: { message: 'Failed to fetch branches' },
      })

      render(<BranchesPage />)

      expect(screen.getByText(/error loading branches/i)).toBeInTheDocument()
      expect(screen.getByText(/failed to fetch branches/i)).toBeInTheDocument()
    })

    it('shows toast error when create branch fails', async () => {
      const user = userEvent.setup()
      const mockCreateBranch = vi.fn().mockRejectedValue(new Error('Create failed'))

      const mockMutation = vi.mocked(require('@/redux/services/organizationsApi').useCreateBranchMutation)
      mockMutation.mockReturnValue([mockCreateBranch, { isLoading: false }])

      const mockToast = vi.mocked(require('sonner').toast)

      render(<BranchesPage />)

      const addButton = screen.getByRole('button', { name: /add branch/i })
      await user.click(addButton)

      await waitFor(() => {
        expect(screen.getByTestId('dialog')).toBeInTheDocument()
      })

      // Fill required fields and submit
      await user.type(screen.getByLabelText(/branch name/i), 'Test Branch')

      const submitButton = screen.getByRole('button', { name: /create branch/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockToast.error).toHaveBeenCalledWith('Failed to create branch')
      })
    })

    it('shows toast error when update branch fails', async () => {
      const user = userEvent.setup()
      const mockUpdateBranch = vi.fn().mockRejectedValue(new Error('Update failed'))

      const mockMutation = vi.mocked(require('@/redux/services/organizationsApi').useUpdateBranchMutation)
      mockMutation.mockReturnValue([mockUpdateBranch, { isLoading: false }])

      const mockToast = vi.mocked(require('sonner').toast)

      render(<BranchesPage />)

      const editButtons = screen.getAllByText('Edit')
      await user.click(editButtons[0])

      await waitFor(() => {
        expect(screen.getByDisplayValue('Downtown Branch')).toBeInTheDocument()
      })

      const submitButton = screen.getByRole('button', { name: /update branch/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockToast.error).toHaveBeenCalledWith('Failed to update branch')
      })
    })

    it('shows toast error when delete branch fails', async () => {
      const user = userEvent.setup()
      const mockDeleteBranch = vi.fn().mockRejectedValue(new Error('Delete failed'))

      const mockMutation = vi.mocked(require('@/redux/services/organizationsApi').useDeleteBranchMutation)
      mockMutation.mockReturnValue([mockDeleteBranch, { isLoading: false }])

      const mockToast = vi.mocked(require('sonner').toast)

      render(<BranchesPage />)

      const deleteButtons = screen.getAllByText('Delete')
      await user.click(deleteButtons[0])

      await waitFor(() => {
        expect(screen.getByText('Delete Branch')).toBeInTheDocument()
      })

      const confirmButton = screen.getByRole('button', { name: /delete/i })
      await user.click(confirmButton)

      await waitFor(() => {
        expect(mockToast.error).toHaveBeenCalledWith('Failed to delete branch')
      })
    })
  })
})