'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { Link, useRouter } from '@/i18n/navigation';
import { useSearchParams } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { useAuth } from '@/hooks/useAuth';
import { signIn } from 'next-auth/react';

export default function RegisterPage() {
  const t = useTranslations('auth');
  const common = useTranslations('common');
  const locale = useLocale();
  const router = useRouter();
  const searchParams = useSearchParams();
  const message = searchParams.get('message');

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Use NextAuth hook
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  // Check if user is already logged in
  useEffect(() => {
    if (isAuthenticated) {
      console.log('User is already authenticated, redirecting to dashboard');
      toast.success('Already logged in');
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  // Show message from URL if present
  useEffect(() => {
    if (message) {
      toast.info(message);
    }
  }, [message]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    // Validate passwords match
    if (password !== confirmPassword) {
      toast.error(common('passwordMismatch'));
      setIsLoading(false);
      return;
    }

    try {
      console.log("Submitting registration data:", { email, name: name || "(not provided)" });

      // Use NextAuth signIn with a special registration mode
      // We'll modify the credentials provider to handle registration
      const signInResult = await signIn('credentials', {
        email,
        password,
        name: name || '',
        isRegistration: 'true', // Flag to indicate this is registration
        redirect: false,
      });

      if (signInResult?.error) {
        // Handle different types of errors
        if (signInResult.error.includes('already exists')) {
          toast.error('Registration failed', {
            description: 'An account with this email already exists. Please sign in instead.',
          });
        } else {
          toast.error('Registration failed', {
            description: signInResult.error,
          });
        }
      } else if (signInResult?.ok) {
        toast.success('Welcome! Account created and signed in successfully');
        router.push('/dashboard');
      }
    } catch (err: any) {
      console.error('Registration request failed:', err);
      const errorMessage = err.message || 'Registration failed';
      toast.error('Registration failed', { description: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleRegister = async () => {
    try {
      await signIn('google', { callbackUrl: '/dashboard' });
    } catch (err: any) {
      console.error('Google registration failed:', err);
      const errorMessage = err.message || 'Google registration failed';
      toast.error('Google registration failed', { description: errorMessage });
    }
  };

  // Show loading state while checking session
  if (authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>{common('loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">{common('createAccount')}</CardTitle>
          <CardDescription>{common('registerDetails')}</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">{common('name')} ({common('optional')})</Label>
              <Input
                id="name"
                name="name"
                type="text"
                placeholder="John Doe"
                value={name}
                onChange={(e) => setName(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">{common('email')}</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">{common('password')}</Label>
              <Input
                id="password"
                name="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading}
              />
              <p className="text-xs text-muted-foreground">{common('passwordRequirements')}</p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">{common('confirmPassword')}</Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {common('processing')}
                </>
              ) : (
                common('signUp')
              )}
            </Button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
            </div>

            <Button
              type="button"
              variant="outline"
              className="w-full bg-blue-50 hover:bg-blue-100 border-blue-200"
              onClick={handleGoogleRegister}
              disabled={isLoading}
            >
              <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Continue with Google (Recommended)
            </Button>

            <div className="text-xs text-muted-foreground text-center">
              💡 Quick signup: Use Google above. Email/password creates a new account.
            </div>
          </form>
        </CardContent>
        <CardFooter className="text-center text-sm">
          {common('alreadyHaveAccount')}{' '}
          <Link href={`/${locale}/login`} className="underline hover:text-primary">
            {common('signIn')}
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
