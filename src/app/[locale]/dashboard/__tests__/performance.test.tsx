import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import DashboardPage from '../page'

// Mock performance API
const mockPerformance = {
  now: vi.fn(),
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByType: vi.fn(),
  getEntriesByName: vi.fn(),
}

Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true,
})

// Mock all dependencies
vi.mock('@/redux/services/dashboardApi', () => ({
  useGetFinancialSummaryQuery: vi.fn(),
  useGetAccountsReceivableSummaryQuery: vi.fn(),
  useGetAccountsPayableSummaryQuery: vi.fn(),
  useGetTopCustomersQuery: vi.fn(),
  useGetTopVendorsQuery: vi.fn(),
  useGetRecentTransactionsQuery: vi.fn(),
  useGetCashFlowDataQuery: vi.fn(),
}))

vi.mock('@/hooks/useI18n', () => ({
  useI18n: () => ({
    t: (key: string) => key,
    formatCurrency: (amount: number) => `$${amount?.toFixed(2) || '0.00'}`,
    formatLongDate: (date: Date) => date?.toLocaleDateString() || 'Invalid Date',
  }),
}))

vi.mock('@/hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleApiError: vi.fn(),
  }),
}))

vi.mock('@/components/common', () => ({
  ErrorBoundary: ({ children }: any) => <div data-testid="error-boundary">{children}</div>,
  StateManager: ({ children, isLoading, error, data }: any) => {
    if (isLoading) return <div data-testid="loading">Loading...</div>
    if (error) return <div data-testid="error">Error occurred</div>
    if (!data) return <div data-testid="no-data">No data</div>
    return <div data-testid="state-manager">{children}</div>
  },
  FormattedCurrency: ({ amount }: any) => <span data-testid="formatted-currency">${amount?.toFixed(2) || '0.00'}</span>,
  DateTime: ({ date }: any) => <span data-testid="date-time">{date?.toLocaleDateString() || 'Invalid Date'}</span>,
}))

// Mock UI components with minimal overhead
vi.mock('@/components/ui/card', () => ({
  Card: ({ children }: any) => <div data-testid="card">{children}</div>,
  CardContent: ({ children }: any) => <div data-testid="card-content">{children}</div>,
  CardDescription: ({ children }: any) => <div data-testid="card-description">{children}</div>,
  CardFooter: ({ children }: any) => <div data-testid="card-footer">{children}</div>,
  CardHeader: ({ children }: any) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children }: any) => <div data-testid="card-title">{children}</div>,
}))

vi.mock('@/components/ui/table', () => ({
  Table: ({ children }: any) => <table data-testid="table">{children}</table>,
  TableBody: ({ children }: any) => <tbody data-testid="table-body">{children}</tbody>,
  TableCell: ({ children }: any) => <td data-testid="table-cell">{children}</td>,
  TableHead: ({ children }: any) => <th data-testid="table-head">{children}</th>,
  TableHeader: ({ children }: any) => <thead data-testid="table-header">{children}</thead>,
  TableRow: ({ children }: any) => <tr data-testid="table-row">{children}</tr>,
}))

vi.mock('@/components/ui/select', () => ({
  Select: ({ children }: any) => <div data-testid="select">{children}</div>,
  SelectContent: ({ children }: any) => <div data-testid="select-content">{children}</div>,
  SelectItem: ({ children }: any) => <div data-testid="select-item">{children}</div>,
  SelectTrigger: ({ children }: any) => <div data-testid="select-trigger">{children}</div>,
  SelectValue: ({ children }: any) => <div data-testid="select-value">{children}</div>,
}))

vi.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children }: any) => <div data-testid="tabs">{children}</div>,
  TabsContent: ({ children }: any) => <div data-testid="tabs-content">{children}</div>,
  TabsList: ({ children }: any) => <div data-testid="tabs-list">{children}</div>,
  TabsTrigger: ({ children }: any) => <div data-testid="tabs-trigger">{children}</div>,
}))

vi.mock('@/components/ui/skeleton', () => ({
  Skeleton: () => <div data-testid="skeleton" />,
}))

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick }: any) => <button data-testid="button" onClick={onClick}>{children}</button>,
}))

vi.mock('lucide-react', () => {
  const createMockIcon = (name: string) =>
    () => <span data-testid={`${name}-icon`} />

  return new Proxy({}, {
    get: (target, prop) => {
      if (typeof prop === 'string') {
        return createMockIcon(prop)
      }
      return undefined
    }
  })
})

describe('Dashboard Performance Tests', () => {
  const mockApiHooks = {
    useGetFinancialSummaryQuery: vi.fn(),
    useGetAccountsReceivableSummaryQuery: vi.fn(),
    useGetAccountsPayableSummaryQuery: vi.fn(),
    useGetTopCustomersQuery: vi.fn(),
    useGetTopVendorsQuery: vi.fn(),
    useGetRecentTransactionsQuery: vi.fn(),
    useGetCashFlowDataQuery: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Reset performance mock
    mockPerformance.now.mockReturnValue(0)
    
    // Setup default mock returns
    Object.values(mockApiHooks).forEach(hook => {
      hook.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })
    })

    // Apply mocks to the actual module
    const dashboardApi = vi.mocked(await import('@/redux/services/dashboardApi'))
    Object.assign(dashboardApi, mockApiHooks)
  })

  describe('Render Performance', () => {
    it('renders initial page within performance budget', () => {
      let callCount = 0
      mockPerformance.now.mockImplementation(() => {
        callCount++
        return callCount === 1 ? 0 : 50 // 50ms render time
      })

      const startTime = performance.now()
      render(<DashboardPage />)
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(100) // Should render within 100ms
    })

    it('handles loading states efficiently', () => {
      // Mock loading state
      Object.values(mockApiHooks).forEach(hook => {
        hook.mockReturnValue({
          data: null,
          isLoading: true,
          error: null,
          refetch: vi.fn(),
        })
      })

      let callCount = 0
      mockPerformance.now.mockImplementation(() => {
        callCount++
        return callCount === 1 ? 0 : 30 // 30ms render time for loading state
      })

      const startTime = performance.now()
      render(<DashboardPage />)
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(50) // Loading state should be very fast
    })

    it('renders with data efficiently', () => {
      // Mock successful data state
      mockApiHooks.useGetFinancialSummaryQuery.mockReturnValue({
        data: {
          total_revenue: 125000,
          total_expenses: 85000,
          net_income: 40000,
          accounts_receivable: 25000,
          accounts_payable: 15000,
          cash_balance: 50000,
        },
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })

      mockApiHooks.useGetTopCustomersQuery.mockReturnValue({
        data: Array.from({ length: 10 }, (_, i) => ({
          id: `customer-${i}`,
          name: `Customer ${i}`,
          total_sales: Math.random() * 10000,
          open_invoices: Math.floor(Math.random() * 5),
        })),
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })

      let callCount = 0
      mockPerformance.now.mockImplementation(() => {
        callCount++
        return callCount === 1 ? 0 : 80 // 80ms render time with data
      })

      const startTime = performance.now()
      render(<DashboardPage />)
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(150) // Should render with data within 150ms
    })
  })

  describe('Memory Usage', () => {
    it('does not create memory leaks with multiple renders', () => {
      const initialMemory = process.memoryUsage().heapUsed

      // Render multiple times
      for (let i = 0; i < 10; i++) {
        const { unmount } = render(<DashboardPage />)
        unmount()
      }

      const finalMemory = process.memoryUsage().heapUsed
      const memoryIncrease = finalMemory - initialMemory

      // Memory increase should be minimal (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
    })
  })

  describe('Large Dataset Performance', () => {
    it('handles large customer datasets efficiently', () => {
      // Mock large dataset
      const largeCustomerList = Array.from({ length: 1000 }, (_, i) => ({
        id: `customer-${i}`,
        name: `Customer ${i}`,
        total_sales: Math.random() * 10000,
        open_invoices: Math.floor(Math.random() * 5),
      }))

      mockApiHooks.useGetTopCustomersQuery.mockReturnValue({
        data: largeCustomerList,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })

      let callCount = 0
      mockPerformance.now.mockImplementation(() => {
        callCount++
        return callCount === 1 ? 0 : 200 // 200ms for large dataset
      })

      const startTime = performance.now()
      render(<DashboardPage />)
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(300) // Should handle large datasets within 300ms
    })

    it('handles complex cash flow data efficiently', () => {
      // Mock complex cash flow data
      const complexCashFlowData = {
        labels: Array.from({ length: 24 }, (_, i) => `Month ${i + 1}`),
        income: Array.from({ length: 24 }, () => Math.random() * 50000),
        expenses: Array.from({ length: 24 }, () => Math.random() * 40000),
      }

      mockApiHooks.useGetCashFlowDataQuery.mockReturnValue({
        data: complexCashFlowData,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })

      let callCount = 0
      mockPerformance.now.mockImplementation(() => {
        callCount++
        return callCount === 1 ? 0 : 150 // 150ms for complex data
      })

      const startTime = performance.now()
      render(<DashboardPage />)
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(250) // Should handle complex data within 250ms
    })
  })

  describe('Re-render Performance', () => {
    it('optimizes re-renders when data changes', () => {
      const { rerender } = render(<DashboardPage />)

      // Change data and re-render
      mockApiHooks.useGetFinancialSummaryQuery.mockReturnValue({
        data: {
          total_revenue: 150000, // Changed value
          total_expenses: 85000,
          net_income: 65000,
          accounts_receivable: 25000,
          accounts_payable: 15000,
          cash_balance: 50000,
        },
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })

      let callCount = 0
      mockPerformance.now.mockImplementation(() => {
        callCount++
        return callCount === 1 ? 0 : 25 // 25ms for re-render
      })

      const startTime = performance.now()
      rerender(<DashboardPage />)
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(50) // Re-renders should be very fast
    })
  })
})
