import { render, screen, fireEvent, waitFor, within } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import userEvent from '@testing-library/user-event'
import DashboardPage from '../page'

// Mock Lucide React icons specifically for dashboard
vi.mock('lucide-react', () => {
  const createMockIcon = (name: string) =>
    ({ className, ...props }: any) => <span data-testid={`${name}-icon`} className={className} {...props} />

  return new Proxy({}, {
    get: (target, prop) => {
      if (typeof prop === 'string') {
        return createMockIcon(prop)
      }
      return undefined
    }
  })
})

// Mock custom hooks
vi.mock('@/hooks/useI18n', () => ({
  useI18n: () => ({
    t: (key: string) => key,
    formatCurrency: (amount: number) => `$${amount?.toFixed(2) || '$0.00'}`,
    formatLongDate: (date: Date) => date?.toLocaleDateString() || 'Invalid Date',
  }),
}))

vi.mock('@/hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleApiError: vi.fn(),
  }),
}))

// Mock components to avoid complex dependencies
vi.mock('@/components/common/StateManager', () => ({
  default: ({ children, isLoading, error, data }: any) => {
    if (isLoading) return <div data-testid="loading">Loading...</div>
    if (error) return <div data-testid="error">Error occurred</div>
    if (!data) return <div data-testid="no-data">No data</div>
    return <div data-testid="state-manager">{children}</div>
  },
}))

vi.mock('@/components/common', () => ({
  ErrorBoundary: ({ children }: any) => <div data-testid="error-boundary">{children}</div>,
  StateManager: ({ children, isLoading, error, data }: any) => {
    if (isLoading) return <div data-testid="loading">Loading...</div>
    if (error) return <div data-testid="error">Error occurred</div>
    if (!data) return <div data-testid="no-data">No data</div>
    return <div data-testid="state-manager">{children}</div>
  },
  FormattedCurrency: ({ amount }: any) => <span data-testid="formatted-currency">${amount?.toFixed(2) || '0.00'}</span>,
  DateTime: ({ date }: any) => <span data-testid="date-time">{date?.toLocaleDateString() || 'Invalid Date'}</span>,
}))

// Mock UI components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className, ...props }: any) => <div data-testid="card" className={className} {...props}>{children}</div>,
  CardContent: ({ children, className, ...props }: any) => <div data-testid="card-content" className={className} {...props}>{children}</div>,
  CardDescription: ({ children, className, ...props }: any) => <div data-testid="card-description" className={className} {...props}>{children}</div>,
  CardFooter: ({ children, className, ...props }: any) => <div data-testid="card-footer" className={className} {...props}>{children}</div>,
  CardHeader: ({ children, className, ...props }: any) => <div data-testid="card-header" className={className} {...props}>{children}</div>,
  CardTitle: ({ children, className, ...props }: any) => <div data-testid="card-title" className={className} {...props}>{children}</div>,
}))

vi.mock('@/components/ui/table', () => ({
  Table: ({ children, ...props }: any) => <table data-testid="table" {...props}>{children}</table>,
  TableBody: ({ children, ...props }: any) => <tbody data-testid="table-body" {...props}>{children}</tbody>,
  TableCell: ({ children, ...props }: any) => <td data-testid="table-cell" {...props}>{children}</td>,
  TableHead: ({ children, ...props }: any) => <th data-testid="table-head" {...props}>{children}</th>,
  TableHeader: ({ children, ...props }: any) => <thead data-testid="table-header" {...props}>{children}</thead>,
  TableRow: ({ children, ...props }: any) => <tr data-testid="table-row" {...props}>{children}</tr>,
}))

vi.mock('@/components/ui/select', () => ({
  Select: ({ children, onValueChange, defaultValue, ...props }: any) => (
    <div data-testid="select" {...props}>
      <select onChange={(e) => onValueChange?.(e.target.value)} defaultValue={defaultValue}>
        {children}
      </select>
    </div>
  ),
  SelectContent: ({ children, ...props }: any) => <div data-testid="select-content" {...props}>{children}</div>,
  SelectItem: ({ children, value, ...props }: any) => <option data-testid="select-item" value={value} {...props}>{children}</option>,
  SelectTrigger: ({ children, ...props }: any) => <div data-testid="select-trigger" {...props}>{children}</div>,
  SelectValue: ({ placeholder, ...props }: any) => <span data-testid="select-value" {...props}>{placeholder}</span>,
}))

vi.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, defaultValue, ...props }: any) => <div data-testid="tabs" data-default-value={defaultValue} {...props}>{children}</div>,
  TabsContent: ({ children, value, ...props }: any) => <div data-testid="tabs-content" data-value={value} {...props}>{children}</div>,
  TabsList: ({ children, ...props }: any) => <div data-testid="tabs-list" {...props}>{children}</div>,
  TabsTrigger: ({ children, value, ...props }: any) => <button data-testid="tabs-trigger" data-value={value} {...props}>{children}</button>,
}))

vi.mock('@/components/ui/skeleton', () => ({
  Skeleton: ({ className, ...props }: any) => <div data-testid="skeleton" className={className} {...props} />,
}))

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => <button data-testid="button" onClick={onClick} {...props}>{children}</button>,
}))

// Mock the dashboard API hooks with proper implementation
const mockApiHooks = {
  useGetFinancialSummaryQuery: vi.fn(),
  useGetAccountsReceivableSummaryQuery: vi.fn(),
  useGetAccountsPayableSummaryQuery: vi.fn(),
  useGetTopCustomersQuery: vi.fn(),
  useGetTopVendorsQuery: vi.fn(),
  useGetRecentTransactionsQuery: vi.fn(),
  useGetCashFlowDataQuery: vi.fn(),
}

vi.mock('@/redux/services/dashboardApi', () => mockApiHooks)

// Mock data for testing
const mockFinancialSummary = {
  total_revenue: 125000,
  total_expenses: 85000,
  net_income: 40000,
  accounts_receivable: 25000,
  accounts_payable: 15000,
  cash_balance: 50000,
}

const mockTopCustomers = [
  { id: '1', name: 'Customer A', total_sales: 15000, open_invoices: 2 },
  { id: '2', name: 'Customer B', total_sales: 12000, open_invoices: 1 },
  { id: '3', name: 'Customer C', total_sales: 8000, open_invoices: 0 },
]

const mockTopVendors = [
  { id: '1', name: 'Vendor A', total_purchases: 8000, open_bills: 1 },
  { id: '2', name: 'Vendor B', total_purchases: 6000, open_bills: 2 },
]

const mockRecentTransactions = [
  { id: '1', type: 'invoice', description: 'Invoice #001', amount: 1500, date: new Date('2024-01-15') },
  { id: '2', type: 'bill', description: 'Bill #001', amount: -800, date: new Date('2024-01-14') },
]

const mockCashFlowData = {
  labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
  income: [10000, 12000, 15000, 13000, 16000, 18000],
  expenses: [8000, 9000, 11000, 10000, 12000, 13000],
}

const mockARSummary = {
  current: 15000,
  days_1_30: 8000,
  days_31_60: 2000,
  days_61_90: 0,
  days_over_90: 0,
  total: 25000,
}

const mockAPSummary = {
  current: 10000,
  days_1_30: 5000,
  days_31_60: 0,
  days_61_90: 0,
  days_over_90: 0,
  total: 15000,
}

describe('DashboardPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Setup default successful mock returns
    mockApiHooks.useGetFinancialSummaryQuery.mockReturnValue({
      data: mockFinancialSummary,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    mockApiHooks.useGetAccountsReceivableSummaryQuery.mockReturnValue({
      data: mockARSummary,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    mockApiHooks.useGetAccountsPayableSummaryQuery.mockReturnValue({
      data: mockAPSummary,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    mockApiHooks.useGetTopCustomersQuery.mockReturnValue({
      data: mockTopCustomers,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    mockApiHooks.useGetTopVendorsQuery.mockReturnValue({
      data: mockTopVendors,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    mockApiHooks.useGetRecentTransactionsQuery.mockReturnValue({
      data: mockRecentTransactions,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    mockApiHooks.useGetCashFlowDataQuery.mockReturnValue({
      data: mockCashFlowData,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Page Rendering', () => {
    it('renders dashboard page with proper structure', () => {
      render(<DashboardPage />)

      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByTestId('error-boundary')).toBeInTheDocument()
    })

    it('renders period selector', () => {
      render(<DashboardPage />)

      expect(screen.getByTestId('select')).toBeInTheDocument()
    })

    it('renders refresh button', () => {
      render(<DashboardPage />)

      const refreshButton = screen.getByTestId('button')
      expect(refreshButton).toBeInTheDocument()
    })
  })

  describe('Financial Summary Cards', () => {
    it('renders all financial summary cards', () => {
      render(<DashboardPage />)

      const cards = screen.getAllByTestId('card')
      expect(cards.length).toBeGreaterThanOrEqual(6) // Revenue, Expenses, Net Income, AR, AP, Cash Balance
    })

    it('displays correct financial data', () => {
      render(<DashboardPage />)

      expect(screen.getByText('totalRevenue')).toBeInTheDocument()
      expect(screen.getByText('totalExpenses')).toBeInTheDocument()
      expect(screen.getByText('netIncome')).toBeInTheDocument()
      expect(screen.getByText('accountsReceivable')).toBeInTheDocument()
      expect(screen.getByText('accountsPayable')).toBeInTheDocument()
      expect(screen.getByText('cashBalance')).toBeInTheDocument()
    })

    it('shows loading state for financial cards', () => {
      mockApiHooks.useGetFinancialSummaryQuery.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
        refetch: vi.fn(),
      })

      render(<DashboardPage />)

      const skeletons = screen.getAllByTestId('skeleton')
      expect(skeletons.length).toBeGreaterThan(0)
    })
  })

  describe('Cash Flow Chart', () => {
    it('renders cash flow chart section', () => {
      render(<DashboardPage />)

      expect(screen.getByText('cashFlow')).toBeInTheDocument()
      expect(screen.getByText('incomeVsExpenses')).toBeInTheDocument()
    })

    it('displays cash flow data when available', () => {
      render(<DashboardPage />)

      expect(screen.getByText('monthlyCashFlow')).toBeInTheDocument()
      expect(screen.getByTestId('table')).toBeInTheDocument()
    })

    it('shows loading state for cash flow chart', () => {
      mockApiHooks.useGetCashFlowDataQuery.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
        refetch: vi.fn(),
      })

      render(<DashboardPage />)

      const skeletons = screen.getAllByTestId('skeleton')
      expect(skeletons.length).toBeGreaterThan(0)
    })

    it('shows no data message when cash flow data is empty', () => {
      mockApiHooks.useGetCashFlowDataQuery.mockReturnValue({
        data: { labels: [], income: [], expenses: [] },
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })

      render(<DashboardPage />)

      expect(screen.getByText('noCashFlowData')).toBeInTheDocument()
    })
  })

  describe('Tabs Section', () => {
    it('renders all tab sections', () => {
      render(<DashboardPage />)

      expect(screen.getByTestId('tabs')).toBeInTheDocument()
      expect(screen.getByText('topCustomers')).toBeInTheDocument()
      expect(screen.getByText('topVendors')).toBeInTheDocument()
      expect(screen.getByText('recentTransactions')).toBeInTheDocument()
      expect(screen.getByText('agingSummary')).toBeInTheDocument()
    })

    it('displays top customers data', () => {
      render(<DashboardPage />)

      // Check for customer names in the table
      expect(screen.getByText('Customer A')).toBeInTheDocument()
      expect(screen.getByText('Customer B')).toBeInTheDocument()
      expect(screen.getByText('Customer C')).toBeInTheDocument()
    })

    it('displays top vendors data', () => {
      render(<DashboardPage />)

      // Check for vendor names
      expect(screen.getByText('Vendor A')).toBeInTheDocument()
      expect(screen.getByText('Vendor B')).toBeInTheDocument()
    })

    it('displays recent transactions', () => {
      render(<DashboardPage />)

      expect(screen.getByText('Invoice #001')).toBeInTheDocument()
      expect(screen.getByText('Bill #001')).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('handles financial summary API error', () => {
      mockApiHooks.useGetFinancialSummaryQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: { message: 'API Error' },
        refetch: vi.fn(),
      })

      render(<DashboardPage />)

      // Should still render the page structure
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
    })

    it('handles multiple API errors gracefully', () => {
      // Mock all APIs to return errors
      Object.values(mockApiHooks).forEach(hook => {
        hook.mockReturnValue({
          data: null,
          isLoading: false,
          error: { message: 'API Error' },
          refetch: vi.fn(),
        })
      })

      render(<DashboardPage />)

      expect(screen.getByText('Dashboard')).toBeInTheDocument()
    })
  })

  describe('User Interactions', () => {
    it('handles period selection change', async () => {
      const user = userEvent.setup()
      render(<DashboardPage />)

      const select = screen.getByTestId('select').querySelector('select')
      expect(select).toBeInTheDocument()

      if (select) {
        await user.selectOptions(select, '7days')
        // Verify the selection was made (implementation would trigger API refetch)
        expect(select.value).toBe('7days')
      }
    })

    it('handles refresh button click', async () => {
      const user = userEvent.setup()
      const mockRefetch = vi.fn()

      mockApiHooks.useGetFinancialSummaryQuery.mockReturnValue({
        data: mockFinancialSummary,
        isLoading: false,
        error: null,
        refetch: mockRefetch,
      })

      render(<DashboardPage />)

      const refreshButton = screen.getByTestId('button')
      await user.click(refreshButton)

      // Verify refresh was called
      expect(mockRefetch).toHaveBeenCalled()
    })
  })

  describe('Performance', () => {
    it('renders within acceptable time', () => {
      const startTime = performance.now()
      render(<DashboardPage />)
      const endTime = performance.now()

      // Should render within 100ms (very generous for testing)
      expect(endTime - startTime).toBeLessThan(100)
    })

    it('handles large datasets efficiently', () => {
      // Mock large datasets
      const largeCustomerList = Array.from({ length: 100 }, (_, i) => ({
        id: `customer-${i}`,
        name: `Customer ${i}`,
        total_sales: Math.random() * 10000,
        open_invoices: Math.floor(Math.random() * 5),
      }))

      mockApiHooks.useGetTopCustomersQuery.mockReturnValue({
        data: largeCustomerList,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })

      const startTime = performance.now()
      render(<DashboardPage />)
      const endTime = performance.now()

      // Should still render efficiently with large datasets
      expect(endTime - startTime).toBeLessThan(200)
    })
  })

  describe('Accessibility', () => {
    it('has proper heading structure', () => {
      render(<DashboardPage />)

      const mainHeading = screen.getByRole('heading', { level: 1 })
      expect(mainHeading).toBeInTheDocument()
      expect(mainHeading).toHaveTextContent('Dashboard')
    })

    it('has accessible card titles', () => {
      render(<DashboardPage />)

      const cardTitles = screen.getAllByTestId('card-title')
      expect(cardTitles.length).toBeGreaterThan(0)

      cardTitles.forEach(title => {
        expect(title).toBeInTheDocument()
      })
    })

    it('has accessible table structure', () => {
      render(<DashboardPage />)

      const tables = screen.getAllByTestId('table')
      expect(tables.length).toBeGreaterThan(0)

      tables.forEach(table => {
        expect(table).toBeInTheDocument()
        // Check for table headers
        const headers = within(table).getAllByTestId('table-head')
        expect(headers.length).toBeGreaterThan(0)
      })
    })

    it('has accessible buttons', () => {
      render(<DashboardPage />)

      const buttons = screen.getAllByTestId('button')
      buttons.forEach(button => {
        expect(button).toBeInTheDocument()
        expect(button).toHaveAttribute('type')
      })
    })

    it('has accessible form controls', () => {
      render(<DashboardPage />)

      const selects = screen.getAllByTestId('select')
      selects.forEach(select => {
        expect(select).toBeInTheDocument()
      })
    })
  })

  describe('Mobile Responsiveness', () => {
    it('renders properly on mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 667,
      })

      render(<DashboardPage />)

      expect(screen.getByText('Dashboard')).toBeInTheDocument()

      // Check that cards are still rendered (responsive grid should handle layout)
      const cards = screen.getAllByTestId('card')
      expect(cards.length).toBeGreaterThan(0)
    })

    it('maintains functionality on tablet viewport', () => {
      // Mock tablet viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      })

      render(<DashboardPage />)

      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByTestId('tabs')).toBeInTheDocument()
    })
  })

  describe('Data Validation', () => {
    it('handles null/undefined data gracefully', () => {
      mockApiHooks.useGetFinancialSummaryQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })

      render(<DashboardPage />)

      expect(screen.getByText('Dashboard')).toBeInTheDocument()
    })

    it('handles malformed data gracefully', () => {
      mockApiHooks.useGetFinancialSummaryQuery.mockReturnValue({
        data: { invalid: 'data' },
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })

      render(<DashboardPage />)

      expect(screen.getByText('Dashboard')).toBeInTheDocument()
    })

    it('validates currency formatting', () => {
      render(<DashboardPage />)

      // Check that currency values are properly formatted
      const formattedCurrencies = screen.getAllByTestId('formatted-currency')
      formattedCurrencies.forEach(currency => {
        expect(currency.textContent).toMatch(/^\$\d+\.\d{2}$/)
      })
    })
  })
})
