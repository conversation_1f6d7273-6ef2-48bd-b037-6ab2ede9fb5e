import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { dashboardApi } from '@/redux/services/dashboardApi'

// Mock fetch for API calls
const mockFetch = vi.fn()
global.fetch = mockFetch

// Create a test store
const createTestStore = () => {
  return configureStore({
    reducer: {
      [dashboardApi.reducerPath]: dashboardApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(dashboardApi.middleware),
  })
}

// Test wrapper component
const createWrapper = (store: any) => {
  return ({ children }: { children: React.ReactNode }) => (
    <Provider store={store}>{children}</Provider>
  )
}

describe('Dashboard API Integration Tests', () => {
  let store: any

  beforeEach(() => {
    store = createTestStore()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('Financial Summary API', () => {
    it('fetches financial summary successfully', async () => {
      const mockData = {
        total_revenue: 125000,
        total_expenses: 85000,
        net_income: 40000,
        accounts_receivable: 25000,
        accounts_payable: 15000,
        cash_balance: 50000,
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockData,
      })

      const { result } = renderHook(
        () => dashboardApi.useGetFinancialSummaryQuery({}),
        { wrapper: createWrapper(store) }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockData)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/dashboard/financial-summary'),
        expect.any(Object)
      )
    })

    it('handles financial summary API error', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const { result } = renderHook(
        () => dashboardApi.useGetFinancialSummaryQuery({}),
        { wrapper: createWrapper(store) }
      )

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeDefined()
    })

    it('includes date parameters in financial summary request', async () => {
      const mockData = { total_revenue: 100000 }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockData,
      })

      const { result } = renderHook(
        () => dashboardApi.useGetFinancialSummaryQuery({
          startDate: '2024-01-01',
          endDate: '2024-01-31'
        }),
        { wrapper: createWrapper(store) }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('start_date=2024-01-01'),
        expect.any(Object)
      )
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('end_date=2024-01-31'),
        expect.any(Object)
      )
    })
  })

  describe('Top Customers API', () => {
    it('fetches top customers successfully', async () => {
      const mockData = [
        { id: '1', name: 'Customer A', total_sales: 15000, open_invoices: 2 },
        { id: '2', name: 'Customer B', total_sales: 12000, open_invoices: 1 },
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockData,
      })

      const { result } = renderHook(
        () => dashboardApi.useGetTopCustomersQuery({ limit: 5, period: '30days' }),
        { wrapper: createWrapper(store) }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockData)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/dashboard/top-customers'),
        expect.any(Object)
      )
    })
  })

  describe('Cash Flow API', () => {
    it('fetches cash flow data successfully', async () => {
      const mockData = {
        labels: ['Jan', 'Feb', 'Mar'],
        income: [10000, 12000, 15000],
        expenses: [8000, 9000, 11000],
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockData,
      })

      const { result } = renderHook(
        () => dashboardApi.useGetCashFlowDataQuery({ period: '6months' }),
        { wrapper: createWrapper(store) }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockData)
    })
  })

  describe('API Caching', () => {
    it('caches financial summary data', async () => {
      const mockData = { total_revenue: 125000 }
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockData,
      })

      // First call
      const { result: result1 } = renderHook(
        () => dashboardApi.useGetFinancialSummaryQuery({}),
        { wrapper: createWrapper(store) }
      )

      await waitFor(() => {
        expect(result1.current.isSuccess).toBe(true)
      })

      // Second call with same parameters should use cache
      const { result: result2 } = renderHook(
        () => dashboardApi.useGetFinancialSummaryQuery({}),
        { wrapper: createWrapper(store) }
      )

      await waitFor(() => {
        expect(result2.current.isSuccess).toBe(true)
      })

      // Should only have made one API call due to caching
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })
  })

  describe('Error Recovery', () => {
    it('retries failed requests', async () => {
      // First call fails
      mockFetch.mockRejectedValueOnce(new Error('Network error'))
      
      const { result } = renderHook(
        () => dashboardApi.useGetFinancialSummaryQuery({}),
        { wrapper: createWrapper(store) }
      )

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      // Retry should work
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ total_revenue: 125000 }),
      })

      result.current.refetch()

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })
    })
  })

  describe('Multiple API Calls', () => {
    it('handles concurrent API calls efficiently', async () => {
      const mockFinancialData = { total_revenue: 125000 }
      const mockCustomersData = [{ id: '1', name: 'Customer A' }]
      const mockVendorsData = [{ id: '1', name: 'Vendor A' }]

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockFinancialData,
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockCustomersData,
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockVendorsData,
        })

      const { result: financialResult } = renderHook(
        () => dashboardApi.useGetFinancialSummaryQuery({}),
        { wrapper: createWrapper(store) }
      )

      const { result: customersResult } = renderHook(
        () => dashboardApi.useGetTopCustomersQuery({}),
        { wrapper: createWrapper(store) }
      )

      const { result: vendorsResult } = renderHook(
        () => dashboardApi.useGetTopVendorsQuery({}),
        { wrapper: createWrapper(store) }
      )

      await waitFor(() => {
        expect(financialResult.current.isSuccess).toBe(true)
        expect(customersResult.current.isSuccess).toBe(true)
        expect(vendorsResult.current.isSuccess).toBe(true)
      })

      expect(mockFetch).toHaveBeenCalledTimes(3)
    })
  })
})
