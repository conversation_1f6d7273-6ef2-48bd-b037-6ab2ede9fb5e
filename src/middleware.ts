import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';
import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

// Create the next-intl middleware
const intlMiddleware = createMiddleware(routing);

async function updateSessionWithNextAuth(request: NextRequest): Promise<NextResponse> {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  try {
    // Get NextAuth JWT token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET
    });

    if (token) {
      console.log(`[NextAuth Middleware] Authenticated user: ${token.email}`);

      // Add custom headers with user information
      response.headers.set('x-user-authenticated', 'true');
      response.headers.set('x-user-id', token.userId as string || token.sub || '');
      response.headers.set('x-user-email', token.email || '');

      if (token.name) {
        response.headers.set('x-user-name', token.name as string);
      }

      if (token.merchantId) {
        response.headers.set('x-user-merchant-id', token.merchantId as string);
      }
    } else {
      console.log('[NextAuth Middleware] No authenticated user');
    }
  } catch (error) {
    console.error('[NextAuth Middleware] Error getting token:', error);
  }

  return response;
}

export async function middleware(request: NextRequest) {
  // Handle static files and API routes that should not be processed by intl middleware
  const pathname = request.nextUrl.pathname;

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.includes('.') ||
    pathname.startsWith('/favicon.ico')
  ) {
    // For API routes, still apply authentication middleware
    if (pathname.startsWith('/api')) {
      return await updateSessionWithNextAuth(request);
    }
    return NextResponse.next();
  }

  // Check if the path already has a locale
  const hasLocale = routing.locales.some(locale =>
    pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  // If no locale in path, let next-intl middleware handle the redirect
  if (!hasLocale) {
    return intlMiddleware(request);
  }

  // Extract locale from pathname
  const locale = pathname.split('/')[1];

  // Public routes that don't require authentication (with locale prefix)
  const publicPaths = [
    `/${locale}/login`,
    `/${locale}/register`,
    `/${locale}/forgot-password`,
    `/${locale}/reset-password`,
    `/${locale}/auth/signin`,
    `/${locale}/auth/signout`,
    `/${locale}/auth/error`,
    `/${locale}/auth/confirm`,
    `/${locale}/error`,
  ];

  // Check if the current path is public
  const isPublicPath = publicPaths.some(path => pathname.startsWith(path));

  // Apply authentication middleware
  const response = await updateSessionWithNextAuth(request);

  // If it's a public path, just return the response
  if (isPublicPath) {
    return response;
  }

  // For protected routes, check authentication
  const isAuthenticated = response.headers.get('x-user-authenticated') === 'true';

  // If not authenticated and trying to access a protected route, redirect to login
  if (!isAuthenticated) {
    const loginUrl = new URL(`/${locale}/login`, request.url);
    loginUrl.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(loginUrl);
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api routes (handled separately)
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
    // Explicitly include the root path to ensure locale redirection
    '/',
  ],
};
