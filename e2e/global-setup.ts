import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test setup...');

  // Launch browser for setup
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Wait for the development server to be ready
    console.log('⏳ Waiting for development server...');
    const baseURL = config.projects[0].use.baseURL || 'http://localhost:3000';
    
    // Try to access the home page to ensure server is ready
    let retries = 0;
    const maxRetries = 30; // 30 seconds timeout
    
    while (retries < maxRetries) {
      try {
        await page.goto(baseURL, { timeout: 5000 });
        console.log('✅ Development server is ready');
        break;
      } catch (error) {
        retries++;
        if (retries === maxRetries) {
          throw new Error(`Development server not ready after ${maxRetries} seconds`);
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Perform any global authentication or setup here
    console.log('🔧 Performing global setup tasks...');
    
    // Example: Login and save authentication state
    // await page.goto('/login');
    // await page.fill('[name="email"]', '<EMAIL>');
    // await page.fill('[name="password"]', 'password');
    // await page.click('[type="submit"]');
    // await page.waitForURL('/dashboard');
    // await page.context().storageState({ path: 'e2e/auth.json' });

    console.log('✅ Global setup completed successfully');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export default globalSetup;
