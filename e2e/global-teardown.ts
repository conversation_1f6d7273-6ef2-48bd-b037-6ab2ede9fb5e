import { FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E test teardown...');

  try {
    // Clean up any temporary files
    const tempFiles = [
      'e2e/auth.json',
      'test-results/temp',
    ];

    for (const file of tempFiles) {
      if (fs.existsSync(file)) {
        if (fs.lstatSync(file).isDirectory()) {
          fs.rmSync(file, { recursive: true, force: true });
        } else {
          fs.unlinkSync(file);
        }
        console.log(`🗑️  Cleaned up: ${file}`);
      }
    }

    // Generate test summary
    const resultsDir = 'test-results';
    if (fs.existsSync(resultsDir)) {
      const files = fs.readdirSync(resultsDir);
      const reportFiles = files.filter(file => 
        file.endsWith('.json') || file.endsWith('.xml') || file.endsWith('.html')
      );
      
      if (reportFiles.length > 0) {
        console.log('📊 Test reports generated:');
        reportFiles.forEach(file => {
          console.log(`   - ${path.join(resultsDir, file)}`);
        });
      }
    }

    console.log('✅ Global teardown completed successfully');

  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  }
}

export default globalTeardown;
