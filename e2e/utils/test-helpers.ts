import { Page, expect } from '@playwright/test';

/**
 * Common test utilities for E2E tests
 */

export const TIMEOUTS = {
  SHORT: 5000,
  MEDIUM: 15000,
  LONG: 30000,
  EXTRA_LONG: 60000,
} as const;

export const VIEWPORTS = {
  MOBILE: { width: 375, height: 667 },
  TABLET: { width: 768, height: 1024 },
  DESKTOP: { width: 1920, height: 1080 },
} as const;

/**
 * Wait for page to be fully loaded with no loading indicators
 */
export async function waitForPageLoad(page: Page, timeout = TIMEOUTS.LONG) {
  // Wait for network to be idle
  await page.waitForLoadState('networkidle', { timeout });
  
  // Wait for any loading skeletons to disappear
  await page.waitForFunction(() => {
    const skeletons = document.querySelectorAll('[data-testid="skeleton"]');
    const loadingSpinners = document.querySelectorAll('[data-testid="loading"]');
    return skeletons.length === 0 && loadingSpinners.length === 0;
  }, { timeout });
}

/**
 * Mock API responses for consistent testing
 */
export async function mockAPIResponse(
  page: Page,
  endpoint: string,
  response: any,
  status = 200
) {
  await page.route(`**${endpoint}**`, async (route) => {
    await route.fulfill({
      status,
      contentType: 'application/json',
      body: JSON.stringify(response),
    });
  });
}

/**
 * Mock multiple API endpoints at once
 */
export async function mockMultipleAPIs(
  page: Page,
  mocks: Array<{ endpoint: string; response: any; status?: number }>
) {
  for (const mock of mocks) {
    await mockAPIResponse(page, mock.endpoint, mock.response, mock.status);
  }
}

/**
 * Take a screenshot with a descriptive name
 */
export async function takeScreenshot(
  page: Page,
  name: string,
  options?: { fullPage?: boolean }
) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${name}-${timestamp}.png`;
  
  await page.screenshot({
    path: `test-results/screenshots/${filename}`,
    fullPage: options?.fullPage || false,
  });
  
  console.log(`📸 Screenshot saved: ${filename}`);
}

/**
 * Wait for an element to be visible and stable
 */
export async function waitForElement(
  page: Page,
  selector: string,
  timeout = TIMEOUTS.MEDIUM
) {
  const element = page.locator(selector);
  await expect(element).toBeVisible({ timeout });
  await element.waitFor({ state: 'stable', timeout: TIMEOUTS.SHORT });
  return element;
}

/**
 * Fill form field with validation
 */
export async function fillFormField(
  page: Page,
  selector: string,
  value: string,
  options?: { validate?: boolean }
) {
  const field = await waitForElement(page, selector);
  await field.fill(value);
  
  if (options?.validate !== false) {
    await expect(field).toHaveValue(value);
  }
}

/**
 * Click element with wait and verification
 */
export async function clickElement(
  page: Page,
  selector: string,
  options?: { waitForNavigation?: boolean }
) {
  const element = await waitForElement(page, selector);
  
  if (options?.waitForNavigation) {
    await Promise.all([
      page.waitForNavigation({ timeout: TIMEOUTS.LONG }),
      element.click(),
    ]);
  } else {
    await element.click();
  }
}

/**
 * Select option from dropdown
 */
export async function selectOption(
  page: Page,
  triggerSelector: string,
  optionText: string
) {
  await clickElement(page, triggerSelector);
  await clickElement(page, `text=${optionText}`);
}

/**
 * Check if element exists without throwing
 */
export async function elementExists(page: Page, selector: string): Promise<boolean> {
  try {
    await page.locator(selector).waitFor({ timeout: 1000 });
    return true;
  } catch {
    return false;
  }
}

/**
 * Wait for API call to complete
 */
export async function waitForAPICall(
  page: Page,
  urlPattern: string,
  timeout = TIMEOUTS.MEDIUM
): Promise<any> {
  return new Promise((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error(`API call to ${urlPattern} timed out after ${timeout}ms`));
    }, timeout);

    page.on('response', async (response) => {
      if (response.url().includes(urlPattern)) {
        clearTimeout(timeoutId);
        try {
          const data = await response.json();
          resolve(data);
        } catch {
          resolve(null);
        }
      }
    });
  });
}

/**
 * Verify no console errors
 */
export async function verifyNoConsoleErrors(page: Page): Promise<string[]> {
  const errors: string[] = [];
  
  page.on('console', (msg) => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
    }
  });
  
  return errors;
}

/**
 * Set viewport for responsive testing
 */
export async function setViewport(
  page: Page,
  viewport: keyof typeof VIEWPORTS | { width: number; height: number }
) {
  const size = typeof viewport === 'string' ? VIEWPORTS[viewport] : viewport;
  await page.setViewportSize(size);
}

/**
 * Simulate slow network conditions
 */
export async function simulateSlowNetwork(page: Page) {
  await page.route('**/*', async (route) => {
    // Add 1-2 second delay to simulate slow network
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));
    await route.continue();
  });
}

/**
 * Test accessibility basics
 */
export async function checkBasicAccessibility(page: Page) {
  // Check for main heading
  const headings = page.locator('h1, h2, h3, h4, h5, h6');
  const headingCount = await headings.count();
  expect(headingCount).toBeGreaterThan(0);

  // Check for proper form labels
  const inputs = page.locator('input, select, textarea');
  const inputCount = await inputs.count();
  
  if (inputCount > 0) {
    // At least some inputs should have labels or aria-labels
    const labeledInputs = page.locator('input[aria-label], input[aria-labelledby], label input');
    const labeledCount = await labeledInputs.count();
    expect(labeledCount).toBeGreaterThan(0);
  }
}

/**
 * Performance measurement helper
 */
export async function measurePerformance(
  page: Page,
  action: () => Promise<void>
): Promise<number> {
  const startTime = Date.now();
  await action();
  const endTime = Date.now();
  return endTime - startTime;
}

/**
 * Retry action with exponential backoff
 */
export async function retryAction<T>(
  action: () => Promise<T>,
  maxRetries = 3,
  baseDelay = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await action();
    } catch (error) {
      lastError = error as Error;
      if (i < maxRetries - 1) {
        const delay = baseDelay * Math.pow(2, i);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError!;
}
