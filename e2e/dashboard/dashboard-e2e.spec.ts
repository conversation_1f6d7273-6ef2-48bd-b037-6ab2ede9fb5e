import { test, expect, Page } from '@playwright/test';

// Test configuration
const DASHBOARD_URL = '/en/dashboard';
const TIMEOUT = 30000;

// Mock data for testing
const mockFinancialData = {
  total_revenue: 125000,
  total_expenses: 85000,
  net_income: 40000,
  cash_balance: 50000,
  accounts_receivable: 25000,
  accounts_payable: 15000,
};

const mockCustomers = [
  { id: '1', name: 'Customer A', total_sales: 15000, open_invoices: 2000 },
  { id: '2', name: 'Customer B', total_sales: 12000, open_invoices: 1500 },
  { id: '3', name: 'Customer C', total_sales: 8000, open_invoices: 0 },
];

const mockVendors = [
  { id: '1', name: 'Vendor A', total_purchases: 8000, open_bills: 1000 },
  { id: '2', name: 'Vendor B', total_purchases: 6000, open_bills: 2000 },
];

const mockTransactions = [
  { id: '1', type: 'invoice', description: 'Invoice #001', amount: 1500, date: '2024-01-15' },
  { id: '2', type: 'bill', description: 'Bill #001', amount: -800, date: '2024-01-14' },
];

const mockCashFlowData = {
  labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
  income: [10000, 12000, 15000, 13000, 16000, 18000],
  expenses: [8000, 9000, 11000, 10000, 12000, 13000],
};

// Helper functions
async function mockDashboardAPIs(page: Page) {
  // Mock financial summary API
  await page.route('**/api/dashboard/financial-summary**', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockFinancialData),
    });
  });

  // Mock top customers API
  await page.route('**/api/dashboard/top-customers**', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockCustomers),
    });
  });

  // Mock top vendors API
  await page.route('**/api/dashboard/top-vendors**', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockVendors),
    });
  });

  // Mock recent transactions API
  await page.route('**/api/dashboard/recent-transactions**', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockTransactions),
    });
  });

  // Mock cash flow API
  await page.route('**/api/dashboard/cash-flow**', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockCashFlowData),
    });
  });

  // Mock AR/AP summary APIs
  await page.route('**/api/dashboard/accounts-receivable-summary**', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        current: 15000,
        days_1_30: 8000,
        days_31_60: 2000,
        days_61_90: 0,
        days_over_90: 0,
        total: 25000,
      }),
    });
  });

  await page.route('**/api/dashboard/accounts-payable-summary**', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        current: 10000,
        days_1_30: 5000,
        days_31_60: 0,
        days_61_90: 0,
        days_over_90: 0,
        total: 15000,
      }),
    });
  });
}

async function waitForDashboardLoad(page: Page) {
  // Wait for the main dashboard heading
  await expect(page.locator('h1')).toContainText('Dashboard', { timeout: TIMEOUT });

  // Wait for financial cards to be visible
  await expect(page.locator('[data-testid="card"]').first()).toBeVisible({ timeout: TIMEOUT });

  // Wait for API calls to complete (no loading skeletons)
  await page.waitForFunction(() => {
    const skeletons = document.querySelectorAll('[data-testid="skeleton"]');
    return skeletons.length === 0;
  }, { timeout: TIMEOUT });
}

test.describe('Dashboard E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock all dashboard APIs before navigation
    await mockDashboardAPIs(page);
  });

  test.describe('Page Loading and Structure', () => {
    test('should load dashboard page successfully', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Verify page title
      await expect(page).toHaveTitle(/Dashboard/);

      // Verify main heading
      await expect(page.locator('h1')).toContainText('Dashboard');

      // Verify no console errors
      const errors: string[] = [];
      page.on('console', (msg) => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });

      // Wait a bit to catch any console errors
      await page.waitForTimeout(2000);
      expect(errors).toHaveLength(0);
    });

    test('should display all financial summary cards', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check for all 6 financial cards
      const cards = page.locator('[data-testid="card"]');
      await expect(cards).toHaveCount(6, { timeout: TIMEOUT });

      // Verify specific card titles
      await expect(page.locator('text=totalRevenue')).toBeVisible();
      await expect(page.locator('text=totalExpenses')).toBeVisible();
      await expect(page.locator('text=netIncome')).toBeVisible();
      await expect(page.locator('text=cashBalance')).toBeVisible();
      await expect(page.locator('text=accountsReceivable')).toBeVisible();
      await expect(page.locator('text=accountsPayable')).toBeVisible();
    });

    test('should display cash flow chart section', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Verify cash flow section
      await expect(page.locator('text=cashFlow')).toBeVisible();
      await expect(page.locator('text=incomeVsExpenses')).toBeVisible();

      // Verify cash flow table
      await expect(page.locator('[data-testid="table"]')).toBeVisible();
      await expect(page.locator('text=monthlyCashFlow')).toBeVisible();
    });

    test('should display tabs section with all tabs', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Verify tabs are present
      await expect(page.locator('[data-testid="tabs"]')).toBeVisible();
      await expect(page.locator('text=topCustomers')).toBeVisible();
      await expect(page.locator('text=topVendors')).toBeVisible();
      await expect(page.locator('text=recentTransactions')).toBeVisible();
      await expect(page.locator('text=agingSummary')).toBeVisible();
    });
  });

  test.describe('Data Display and Accuracy', () => {
    test('should display correct financial data', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Verify financial values are displayed (formatted as currency)
      await expect(page.locator('text=$125,000.00')).toBeVisible(); // Revenue
      await expect(page.locator('text=$85,000.00')).toBeVisible();  // Expenses
      await expect(page.locator('text=$40,000.00')).toBeVisible();  // Net Income
      await expect(page.locator('text=$50,000.00')).toBeVisible();  // Cash Balance
    });

    test('should display top customers data', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Click on customers tab
      await page.locator('[data-testid="tabs-trigger"][data-value="customers"]').click();

      // Verify customer data is displayed
      await expect(page.locator('text=Customer A')).toBeVisible();
      await expect(page.locator('text=Customer B')).toBeVisible();
      await expect(page.locator('text=Customer C')).toBeVisible();

      // Verify customer sales amounts
      await expect(page.locator('text=$15,000.00')).toBeVisible();
      await expect(page.locator('text=$12,000.00')).toBeVisible();
      await expect(page.locator('text=$8,000.00')).toBeVisible();
    });

    test('should display top vendors data', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Click on vendors tab
      await page.locator('[data-testid="tabs-trigger"][data-value="vendors"]').click();

      // Verify vendor data is displayed
      await expect(page.locator('text=Vendor A')).toBeVisible();
      await expect(page.locator('text=Vendor B')).toBeVisible();

      // Verify vendor purchase amounts
      await expect(page.locator('text=$8,000.00')).toBeVisible();
      await expect(page.locator('text=$6,000.00')).toBeVisible();
    });

    test('should display recent transactions', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Click on transactions tab
      await page.locator('[data-testid="tabs-trigger"][data-value="transactions"]').click();

      // Verify transaction data is displayed
      await expect(page.locator('text=Invoice #001')).toBeVisible();
      await expect(page.locator('text=Bill #001')).toBeVisible();
    });

    test('should display cash flow data correctly', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Verify cash flow months are displayed
      await expect(page.locator('text=Jan')).toBeVisible();
      await expect(page.locator('text=Feb')).toBeVisible();
      await expect(page.locator('text=Mar')).toBeVisible();

      // Verify summary totals
      await expect(page.locator('text=totalIncome')).toBeVisible();
      await expect(page.locator('text=totalExpenses')).toBeVisible();
    });
  });

  test.describe('User Interactions', () => {
    test('should handle period selection changes', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Find and click the period selector
      const periodSelector = page.locator('[data-testid="select-trigger"]');
      await expect(periodSelector).toBeVisible();
      await periodSelector.click();

      // Verify dropdown options are visible
      await expect(page.locator('text=Last 7 Days')).toBeVisible();
      await expect(page.locator('text=Last 30 Days')).toBeVisible();
      await expect(page.locator('text=Last 90 Days')).toBeVisible();
      await expect(page.locator('text=Last 6 Months')).toBeVisible();
      await expect(page.locator('text=Last 12 Months')).toBeVisible();
      await expect(page.locator('text=Year to Date')).toBeVisible();

      // Select a different period
      await page.locator('text=Last 7 Days').click();

      // Verify the selection was made
      await expect(periodSelector).toContainText('Last 7 Days');
    });

    test('should handle refresh button click', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Track API calls
      let apiCallCount = 0;
      page.on('request', (request) => {
        if (request.url().includes('/api/dashboard/')) {
          apiCallCount++;
        }
      });

      // Click refresh button
      const refreshButton = page.locator('[data-testid="button"]', { hasText: 'Refresh' });
      await expect(refreshButton).toBeVisible();
      await refreshButton.click();

      // Wait for refresh to complete
      await page.waitForTimeout(1000);

      // Verify API calls were made (should be > 0)
      expect(apiCallCount).toBeGreaterThan(0);
    });

    test('should handle tab navigation', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Test customers tab
      await page.locator('[data-testid="tabs-trigger"][data-value="customers"]').click();
      await expect(page.locator('[data-testid="tabs-content"][data-value="customers"]')).toBeVisible();
      await expect(page.locator('text=Customer A')).toBeVisible();

      // Test vendors tab
      await page.locator('[data-testid="tabs-trigger"][data-value="vendors"]').click();
      await expect(page.locator('[data-testid="tabs-content"][data-value="vendors"]')).toBeVisible();
      await expect(page.locator('text=Vendor A')).toBeVisible();

      // Test transactions tab
      await page.locator('[data-testid="tabs-trigger"][data-value="transactions"]').click();
      await expect(page.locator('[data-testid="tabs-content"][data-value="transactions"]')).toBeVisible();
      await expect(page.locator('text=Invoice #001')).toBeVisible();

      // Test aging tab
      await page.locator('[data-testid="tabs-trigger"][data-value="aging"]').click();
      await expect(page.locator('[data-testid="tabs-content"][data-value="aging"]')).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      // Mock API to return errors
      await page.route('**/api/dashboard/financial-summary**', async (route) => {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal Server Error' }),
        });
      });

      await page.goto(DASHBOARD_URL);

      // Wait for page to load
      await expect(page.locator('h1')).toContainText('Dashboard', { timeout: TIMEOUT });

      // Should still display the page structure
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('[data-testid="select-trigger"]')).toBeVisible();
      await expect(page.locator('[data-testid="button"]')).toBeVisible();
    });

    test('should handle network timeouts', async ({ page }) => {
      // Mock API with delayed response
      await page.route('**/api/dashboard/financial-summary**', async (route) => {
        await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockFinancialData),
        });
      });

      await page.goto(DASHBOARD_URL);

      // Should show loading states
      await expect(page.locator('h1')).toContainText('Dashboard', { timeout: TIMEOUT });

      // Should eventually load data or show error
      await page.waitForTimeout(6000);
    });

    test('should handle empty data responses', async ({ page }) => {
      // Mock APIs to return empty data
      await page.route('**/api/dashboard/top-customers**', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([]),
        });
      });

      await page.route('**/api/dashboard/top-vendors**', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([]),
        });
      });

      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check customers tab shows no data message
      await page.locator('[data-testid="tabs-trigger"][data-value="customers"]').click();
      await expect(page.locator('text=noCustomerData')).toBeVisible();

      // Check vendors tab shows no data message
      await page.locator('[data-testid="tabs-trigger"][data-value="vendors"]').click();
      await expect(page.locator('text=noVendorData')).toBeVisible();
    });
  });

  test.describe('Performance', () => {
    test('should load within acceptable time', async ({ page }) => {
      const startTime = Date.now();

      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      const loadTime = Date.now() - startTime;

      // Should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
    });

    test('should handle large datasets efficiently', async ({ page }) => {
      // Mock large customer dataset
      const largeCustomerList = Array.from({ length: 100 }, (_, i) => ({
        id: `customer-${i}`,
        name: `Customer ${i}`,
        total_sales: Math.random() * 10000,
        open_invoices: Math.random() * 1000,
      }));

      await page.route('**/api/dashboard/top-customers**', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(largeCustomerList),
        });
      });

      const startTime = Date.now();

      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Click customers tab
      await page.locator('[data-testid="tabs-trigger"][data-value="customers"]').click();

      const loadTime = Date.now() - startTime;

      // Should still load efficiently with large dataset
      expect(loadTime).toBeLessThan(8000);
    });
  });

  test.describe('Mobile Responsiveness', () => {
    test('should display correctly on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });

      await mockDashboardAPIs(page);
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Verify main elements are visible on mobile
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('[data-testid="select-trigger"]')).toBeVisible();
      await expect(page.locator('[data-testid="button"]')).toBeVisible();

      // Verify cards are stacked vertically on mobile
      const cards = page.locator('[data-testid="card"]');
      await expect(cards.first()).toBeVisible();

      // Verify tabs work on mobile
      await page.locator('[data-testid="tabs-trigger"][data-value="customers"]').click();
      await expect(page.locator('text=Customer A')).toBeVisible();
    });

    test('should display correctly on tablet devices', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });

      await mockDashboardAPIs(page);
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Verify layout adapts to tablet size
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('[data-testid="card"]').first()).toBeVisible();

      // Verify cash flow chart is visible
      await expect(page.locator('text=cashFlow')).toBeVisible();
      await expect(page.locator('[data-testid="table"]')).toBeVisible();
    });

    test('should handle touch interactions on mobile', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });

      await mockDashboardAPIs(page);
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Test touch interactions
      await page.locator('[data-testid="select-trigger"]').tap();
      await expect(page.locator('text=Last 7 Days')).toBeVisible();

      await page.locator('text=Last 7 Days').tap();
      await expect(page.locator('[data-testid="select-trigger"]')).toContainText('Last 7 Days');

      // Test tab touch interactions
      await page.locator('[data-testid="tabs-trigger"][data-value="vendors"]').tap();
      await expect(page.locator('text=Vendor A')).toBeVisible();
    });
  });

  test.describe('Accessibility', () => {
    test('should have proper heading structure', async ({ page }) => {
      await mockDashboardAPIs(page);
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check main heading
      const mainHeading = page.locator('h1');
      await expect(mainHeading).toBeVisible();
      await expect(mainHeading).toContainText('Dashboard');

      // Check for proper heading hierarchy
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      const headingCount = await headings.count();
      expect(headingCount).toBeGreaterThan(0);
    });

    test('should have accessible form controls', async ({ page }) => {
      await mockDashboardAPIs(page);
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check select has proper attributes
      const select = page.locator('[data-testid="select-trigger"]');
      await expect(select).toBeVisible();

      // Check button has proper attributes
      const button = page.locator('[data-testid="button"]');
      await expect(button).toBeVisible();
      await expect(button).toHaveAttribute('type');
    });

    test('should support keyboard navigation', async ({ page }) => {
      await mockDashboardAPIs(page);
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Test tab navigation
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');

      // Test enter key on focusable elements
      await page.locator('[data-testid="tabs-trigger"][data-value="customers"]').focus();
      await page.keyboard.press('Enter');
      await expect(page.locator('text=Customer A')).toBeVisible();
    });

    test('should have proper table structure', async ({ page }) => {
      await mockDashboardAPIs(page);
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check cash flow table structure
      const table = page.locator('[data-testid="table"]').first();
      await expect(table).toBeVisible();

      // Check for table headers
      const headers = page.locator('[data-testid="table-head"]');
      const headerCount = await headers.count();
      expect(headerCount).toBeGreaterThan(0);

      // Check for table body
      await expect(page.locator('[data-testid="table-body"]')).toBeVisible();
    });

    test('should have accessible card structure', async ({ page }) => {
      await mockDashboardAPIs(page);
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check cards have proper structure
      const cards = page.locator('[data-testid="card"]');
      const cardCount = await cards.count();
      expect(cardCount).toBeGreaterThanOrEqual(6);

      // Check card titles are present
      const cardTitles = page.locator('[data-testid="card-title"]');
      const titleCount = await cardTitles.count();
      expect(titleCount).toBeGreaterThan(0);
    });
  });

  test.describe('Real-time Features', () => {
    test('should handle auto-refresh functionality', async ({ page }) => {
      await mockDashboardAPIs(page);
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Track API calls for auto-refresh
      let refreshCallCount = 0;
      page.on('request', (request) => {
        if (request.url().includes('/api/dashboard/') && request.method() === 'GET') {
          refreshCallCount++;
        }
      });

      // Simulate auto-refresh by clicking refresh button
      const refreshButton = page.locator('[data-testid="button"]', { hasText: 'Refresh' });
      await refreshButton.click();

      await page.waitForTimeout(1000);

      // Verify refresh calls were made
      expect(refreshCallCount).toBeGreaterThan(0);
    });

    test('should maintain state during refresh', async ({ page }) => {
      await mockDashboardAPIs(page);
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Change period selection
      await page.locator('[data-testid="select-trigger"]').click();
      await page.locator('text=Last 7 Days').click();

      // Switch to vendors tab
      await page.locator('[data-testid="tabs-trigger"][data-value="vendors"]').click();
      await expect(page.locator('text=Vendor A')).toBeVisible();

      // Refresh data
      await page.locator('[data-testid="button"]', { hasText: 'Refresh' }).click();
      await page.waitForTimeout(1000);

      // Verify state is maintained
      await expect(page.locator('[data-testid="select-trigger"]')).toContainText('Last 7 Days');
      await expect(page.locator('text=Vendor A')).toBeVisible();
    });
  });
});
