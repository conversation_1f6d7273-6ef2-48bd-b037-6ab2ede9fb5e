import { test, expect } from '@playwright/test';

test.describe('Dashboard Simple E2E Tests', () => {
  test('should load dashboard page', async ({ page }) => {
    // Navigate to dashboard
    await page.goto('/en/dashboard');
    
    // Wait for page to load
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 30000 });
    
    // Verify basic elements are present
    await expect(page.locator('h1')).toBeVisible();
    
    console.log('✅ Dashboard page loaded successfully');
  });

  test('should display financial cards', async ({ page }) => {
    await page.goto('/en/dashboard');
    
    // Wait for dashboard to load
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 30000 });
    
    // Check for financial summary text elements
    await expect(page.locator('text=totalRevenue')).toBeVisible();
    await expect(page.locator('text=totalExpenses')).toBeVisible();
    await expect(page.locator('text=netIncome')).toBeVisible();
    
    console.log('✅ Financial cards are visible');
  });

  test('should have working period selector', async ({ page }) => {
    await page.goto('/en/dashboard');
    
    // Wait for dashboard to load
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 30000 });
    
    // Look for select elements or buttons
    const selectors = [
      'select',
      '[role="combobox"]',
      '[data-testid="select"]',
      'button:has-text("Last")',
      'button:has-text("Period")'
    ];
    
    let selectorFound = false;
    for (const selector of selectors) {
      try {
        const element = page.locator(selector).first();
        if (await element.isVisible({ timeout: 5000 })) {
          selectorFound = true;
          console.log(`✅ Found period selector: ${selector}`);
          break;
        }
      } catch {
        // Continue to next selector
      }
    }
    
    expect(selectorFound).toBe(true);
  });

  test('should have working refresh button', async ({ page }) => {
    await page.goto('/en/dashboard');
    
    // Wait for dashboard to load
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 30000 });
    
    // Look for refresh button
    const refreshSelectors = [
      'button:has-text("Refresh")',
      'button:has-text("refresh")',
      '[aria-label*="refresh"]',
      '[title*="refresh"]'
    ];
    
    let refreshFound = false;
    for (const selector of refreshSelectors) {
      try {
        const element = page.locator(selector).first();
        if (await element.isVisible({ timeout: 5000 })) {
          refreshFound = true;
          console.log(`✅ Found refresh button: ${selector}`);
          break;
        }
      } catch {
        // Continue to next selector
      }
    }
    
    expect(refreshFound).toBe(true);
  });

  test('should display tabs section', async ({ page }) => {
    await page.goto('/en/dashboard');
    
    // Wait for dashboard to load
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 30000 });
    
    // Check for tab-related text
    const tabTexts = ['topCustomers', 'topVendors', 'recentTransactions', 'agingSummary'];
    let tabsFound = 0;
    
    for (const text of tabTexts) {
      try {
        await expect(page.locator(`text=${text}`)).toBeVisible({ timeout: 5000 });
        tabsFound++;
      } catch {
        // Tab might not be visible
      }
    }
    
    expect(tabsFound).toBeGreaterThan(0);
    console.log(`✅ Found ${tabsFound} tab sections`);
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/en/dashboard');
    
    // Wait for dashboard to load
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 30000 });
    
    // Verify main elements are still visible on mobile
    await expect(page.locator('h1')).toBeVisible();
    
    console.log('✅ Dashboard is responsive on mobile');
  });
});
