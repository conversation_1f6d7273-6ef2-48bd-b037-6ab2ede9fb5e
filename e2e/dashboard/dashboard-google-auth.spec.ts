import { test, expect } from '@playwright/test';

test.describe('Dashboard E2E Tests with Google OAuth', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page first
    await page.goto('/en/login');
    
    // Wait for login page to load
    await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
  });

  test('should show Google OAuth option', async ({ page }) => {
    console.log('🔍 Checking for Google OAuth option...');
    
    // Check for Google OAuth button
    const googleButton = page.locator('button:has-text("Google"), button:has-text("Continue with Google")');
    await expect(googleButton).toBeVisible({ timeout: 10000 });
    
    console.log('✅ Google OAuth button found');
  });

  test('should redirect unauthenticated users to login', async ({ page }) => {
    console.log('🔐 Testing unauthenticated access...');
    
    // Try to access dashboard directly without authentication
    await page.goto('/en/dashboard');
    
    // Should be redirected to login page
    await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible({ timeout: 10000 });
    
    // Verify we're on login page
    const currentUrl = page.url();
    expect(currentUrl).toContain('login');
    
    console.log('✅ Unauthenticated users properly redirected to login');
  });

  test('should display login form elements', async ({ page }) => {
    console.log('🔍 Testing login form elements...');
    
    // Check for email input
    await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
    
    // Check for password input
    await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
    
    // Check for submit button
    await expect(page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')).toBeVisible();
    
    // Check for Google OAuth button
    await expect(page.locator('button:has-text("Google"), button:has-text("Continue with Google")')).toBeVisible();
    
    console.log('✅ All login form elements are present');
  });

  test('should handle invalid credentials gracefully', async ({ page }) => {
    console.log('🔐 Testing invalid credentials...');
    
    // Try with invalid credentials
    await page.locator('input[type="email"], input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[type="password"], input[name="password"]').first().fill('wrongpassword');
    
    const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
    await submitButton.click();
    
    // Wait for response
    await page.waitForTimeout(3000);
    
    // Should stay on login page (authentication failed)
    const isStillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
    expect(isStillOnLogin).toBe(true);
    
    console.log('✅ Invalid credentials handled correctly');
  });

  test('should work on mobile devices', async ({ page }) => {
    console.log('📱 Testing mobile responsiveness...');
    
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Verify login form works on mobile
    await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')).toBeVisible();
    await expect(page.locator('button:has-text("Google"), button:has-text("Continue with Google")')).toBeVisible();
    
    console.log('✅ Login form works on mobile');
  });

  test('should display registration link', async ({ page }) => {
    console.log('🔍 Testing registration link...');
    
    // Check for registration link
    const registerLink = page.locator('a:has-text("Sign up"), a:has-text("Register"), a:has-text("Create account")');
    await expect(registerLink).toBeVisible({ timeout: 10000 });
    
    // Click registration link
    await registerLink.click();
    
    // Should navigate to registration page
    await expect(page).toHaveURL(/.*register.*/);
    
    console.log('✅ Registration link works correctly');
  });

  test('should display forgot password link', async ({ page }) => {
    console.log('🔍 Testing forgot password link...');
    
    // Check for forgot password link
    const forgotPasswordLink = page.locator('a:has-text("Forgot"), a:has-text("forgot"), a:has-text("Reset")');
    
    // This might not exist, so we'll check if it's visible
    const isVisible = await forgotPasswordLink.isVisible();
    
    if (isVisible) {
      console.log('✅ Forgot password link found');
    } else {
      console.log('ℹ️  Forgot password link not implemented yet');
    }
  });

  test('should have proper page title and meta', async ({ page }) => {
    console.log('🔍 Testing page metadata...');
    
    // Check page title
    const title = await page.title();
    expect(title).toBeTruthy();
    expect(title.length).toBeGreaterThan(0);
    
    console.log(`✅ Page title: "${title}"`);
  });

  test('should display proper error handling for network issues', async ({ page }) => {
    console.log('🌐 Testing network error handling...');
    
    // Fill in some credentials
    await page.locator('input[type="email"], input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[type="password"], input[name="password"]').first().fill('password123');
    
    // Simulate network failure by going offline
    await page.context().setOffline(true);
    
    // Try to submit
    const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
    await submitButton.click();
    
    // Wait for potential error handling
    await page.waitForTimeout(3000);
    
    // Go back online
    await page.context().setOffline(false);
    
    // Should still be on login page
    const isStillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
    expect(isStillOnLogin).toBe(true);
    
    console.log('✅ Network error handling works');
  });

  test('should validate form inputs', async ({ page }) => {
    console.log('📝 Testing form validation...');
    
    // Try to submit empty form
    const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
    await submitButton.click();
    
    // Check if form validation prevents submission
    const isStillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
    expect(isStillOnLogin).toBe(true);
    
    // Try with invalid email format
    await page.locator('input[type="email"], input[name="email"]').first().fill('invalid-email');
    await page.locator('input[type="password"], input[name="password"]').first().fill('password');
    await submitButton.click();
    
    await page.waitForTimeout(2000);
    
    // Should still be on login page due to validation
    const stillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
    expect(stillOnLogin).toBe(true);
    
    console.log('✅ Form validation works correctly');
  });
});
