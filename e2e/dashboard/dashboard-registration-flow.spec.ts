import { test, expect } from '@playwright/test';

test.describe('Registration and Login Flow E2E Tests', () => {
  const testUser = {
    name: 'E2E Test User',
    email: `e2etest+${Date.now()}@example.com`, // Unique email for each test run
    password: 'TestPass123!'
  };

  test('should complete registration flow', async ({ page }) => {
    console.log('📝 Testing user registration...');
    
    // Navigate to registration page
    await page.goto('/en/register');
    
    // Wait for registration form to load
    await expect(page.locator('h1, h2, text=Create Account, text=Sign Up')).toBeVisible({ timeout: 10000 });
    
    // Fill out registration form
    const nameInput = page.locator('input[name="name"], input[placeholder*="name" i]').first();
    const emailInput = page.locator('input[type="email"], input[name="email"]').first();
    const passwordInput = page.locator('input[type="password"], input[name="password"]').first();
    const confirmPasswordInput = page.locator('input[name="confirmPassword"], input[name="confirm"], input[placeholder*="confirm" i]').first();
    
    // Check if name field exists (it's optional)
    const nameExists = await nameInput.isVisible();
    if (nameExists) {
      await nameInput.fill(testUser.name);
      console.log('✅ Filled name field');
    }
    
    await emailInput.fill(testUser.email);
    await passwordInput.fill(testUser.password);
    
    // Check if confirm password field exists
    const confirmExists = await confirmPasswordInput.isVisible();
    if (confirmExists) {
      await confirmPasswordInput.fill(testUser.password);
      console.log('✅ Filled confirm password field');
    }
    
    // Submit registration form
    const submitButton = page.locator('button[type="submit"], button:has-text("Sign Up"), button:has-text("Create"), button:has-text("Register")').first();
    await expect(submitButton).toBeVisible();
    
    await submitButton.click();
    
    // Wait for response
    await page.waitForTimeout(3000);
    
    // Check if we're redirected or if there's a success message
    const currentUrl = page.url();
    console.log(`Current URL after registration: ${currentUrl}`);
    
    // Registration might redirect to login with a message, or show a confirmation
    const possibleSuccessIndicators = [
      page.locator('text=Check email'),
      page.locator('text=verification'),
      page.locator('text=confirm'),
      page.locator('text=success'),
      page.locator('input[type="email"]') // Redirected to login
    ];
    
    let registrationHandled = false;
    for (const indicator of possibleSuccessIndicators) {
      if (await indicator.isVisible()) {
        registrationHandled = true;
        break;
      }
    }
    
    expect(registrationHandled).toBe(true);
    console.log('✅ Registration form submitted successfully');
  });

  test('should validate registration form', async ({ page }) => {
    console.log('📝 Testing registration form validation...');
    
    await page.goto('/en/register');
    await expect(page.locator('h1, h2, text=Create Account, text=Sign Up')).toBeVisible({ timeout: 10000 });
    
    // Try to submit empty form
    const submitButton = page.locator('button[type="submit"], button:has-text("Sign Up"), button:has-text("Create"), button:has-text("Register")').first();
    await submitButton.click();
    
    // Should still be on registration page
    await page.waitForTimeout(2000);
    const stillOnRegister = await page.locator('input[type="email"], input[name="email"]').isVisible();
    expect(stillOnRegister).toBe(true);
    
    // Try with invalid email
    await page.locator('input[type="email"], input[name="email"]').first().fill('invalid-email');
    await page.locator('input[type="password"], input[name="password"]').first().fill('weak');
    await submitButton.click();
    
    await page.waitForTimeout(2000);
    
    // Should still be on registration page due to validation
    const stillOnRegisterAfterInvalid = await page.locator('input[type="email"], input[name="email"]').isVisible();
    expect(stillOnRegisterAfterInvalid).toBe(true);
    
    console.log('✅ Registration form validation works');
  });

  test('should navigate between login and registration', async ({ page }) => {
    console.log('🔄 Testing navigation between login and registration...');
    
    // Start on login page
    await page.goto('/en/login');
    await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
    
    // Find and click registration link
    const registerLink = page.locator('a:has-text("Sign up"), a:has-text("Register"), a:has-text("Create account")');
    await expect(registerLink).toBeVisible();
    await registerLink.click();
    
    // Should be on registration page
    await expect(page).toHaveURL(/.*register.*/);
    await expect(page.locator('h1, h2, text=Create Account, text=Sign Up')).toBeVisible();
    
    // Find and click login link
    const loginLink = page.locator('a:has-text("Sign in"), a:has-text("Login"), a:has-text("Sign In")');
    await expect(loginLink).toBeVisible();
    await loginLink.click();
    
    // Should be back on login page
    await expect(page).toHaveURL(/.*login.*/);
    await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
    
    console.log('✅ Navigation between login and registration works');
  });

  test('should display proper error for existing email', async ({ page }) => {
    console.log('📧 Testing duplicate email handling...');
    
    await page.goto('/en/register');
    await expect(page.locator('h1, h2, text=Create Account, text=Sign Up')).toBeVisible({ timeout: 10000 });
    
    // Try to register with a common email that might already exist
    await page.locator('input[type="email"], input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[type="password"], input[name="password"]').first().fill('TestPass123!');
    
    const confirmPasswordInput = page.locator('input[name="confirmPassword"], input[name="confirm"], input[placeholder*="confirm" i]').first();
    const confirmExists = await confirmPasswordInput.isVisible();
    if (confirmExists) {
      await confirmPasswordInput.fill('TestPass123!');
    }
    
    const submitButton = page.locator('button[type="submit"], button:has-text("Sign Up"), button:has-text("Create"), button:has-text("Register")').first();
    await submitButton.click();
    
    // Wait for response
    await page.waitForTimeout(3000);
    
    // Should handle the duplicate email gracefully (either error message or redirect)
    const isStillOnRegister = await page.locator('input[type="email"], input[name="email"]').isVisible();
    
    // This test just ensures the system doesn't crash with duplicate emails
    console.log('✅ Duplicate email handled gracefully');
  });

  test('should work on mobile devices', async ({ page }) => {
    console.log('📱 Testing registration on mobile...');
    
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/en/register');
    
    // Verify form elements are visible on mobile
    await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"], button:has-text("Sign Up"), button:has-text("Create"), button:has-text("Register")')).toBeVisible();
    
    // Check if Google OAuth is available on mobile
    const googleButton = page.locator('button:has-text("Google"), button:has-text("Continue with Google")');
    const googleExists = await googleButton.isVisible();
    
    if (googleExists) {
      console.log('✅ Google OAuth available on mobile');
    } else {
      console.log('ℹ️  Google OAuth not visible on mobile (might be in a different location)');
    }
    
    console.log('✅ Registration form works on mobile');
  });

  test('should show password requirements', async ({ page }) => {
    console.log('🔒 Testing password requirements display...');
    
    await page.goto('/en/register');
    await expect(page.locator('h1, h2, text=Create Account, text=Sign Up')).toBeVisible({ timeout: 10000 });
    
    // Check if password requirements are displayed
    const pageText = await page.textContent('body');
    const hasPasswordRequirements = pageText?.includes('8 characters') || 
                                   pageText?.includes('number') || 
                                   pageText?.includes('special character') ||
                                   pageText?.includes('uppercase') ||
                                   pageText?.includes('lowercase');
    
    if (hasPasswordRequirements) {
      console.log('✅ Password requirements are displayed');
    } else {
      console.log('ℹ️  Password requirements not explicitly shown');
    }
    
    // Try with a weak password to test validation
    await page.locator('input[type="email"], input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[type="password"], input[name="password"]').first().fill('weak');
    
    const submitButton = page.locator('button[type="submit"], button:has-text("Sign Up"), button:has-text("Create"), button:has-text("Register")').first();
    await submitButton.click();
    
    await page.waitForTimeout(2000);
    
    // Should still be on registration page due to weak password
    const stillOnRegister = await page.locator('input[type="email"], input[name="email"]').isVisible();
    expect(stillOnRegister).toBe(true);
    
    console.log('✅ Weak password validation works');
  });
});
